import { type NextRequest, NextResponse } from "next/server"
import { BackupService } from "@/lib/database-service"

export async function GET() {
  try {
    const result = await BackupService.createBackup()

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      backup: result.data,
    })
  } catch (error) {
    console.error("Erro ao criar backup:", error)
    return NextResponse.json({ success: false, error: "Erro ao criar backup" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const result = await BackupService.restoreBackup(body)

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao restaurar backup:", error)
    return NextResponse.json({ success: false, error: "Erro ao restaurar backup" }, { status: 500 })
  }
}

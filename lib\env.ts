import { z } from "zod"

const envSchema = z.object({
  DATABASE_URL: z.string().url().optional(),
  NEON_DATABASE_URL: z.string().url().optional(),
  POSTGRES_URL: z.string().url().optional(),
  OPENROUTER_API_KEY: z.string().optional(),
  OLLAMA_BASE_URL: z.string().url().optional(),
  ENCRYPTION_KEY: z.string().min(32).optional(),
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  VERCEL_URL: z.string().optional(),
  NEXT_PUBLIC_SITE_URL: z.string().url().optional(),
  MAX_CONNECTIONS: z.string().optional(),
  CONNECTION_TIMEOUT: z.string().optional(),
  QUERY_TIMEOUT: z.string().optional(),
  SESSION_SECRET: z.string().optional(),
  RATE_LIMIT_ENABLED: z.string().optional(),
  METRICS_ENABLED: z.string().optional(),
  BACKUP_ENABLED: z.string().optional(),
  LOG_LEVEL: z.string().optional(),
})

type Env = z.infer<typeof envSchema>

let env: Env

try {
  env = envSchema.parse(process.env)
} catch (error) {
  console.error("❌ Invalid environment variables:", error)
  env = {
    NODE_ENV: "development",
  }
}

export { env }

export function checkCriticalEnvVars(): {
  isValid: boolean
  missing: string[]
  warnings: string[]
} {
  const missing: string[] = []
  const warnings: string[] = []

  // Check for database URL
  if (!env.DATABASE_URL && !env.POSTGRES_URL) {
    warnings.push("DATABASE_URL or POSTGRES_URL not set - will use local SQLite")
  }

  // Check for AI provider keys
  if (!env.OPENROUTER_API_KEY) {
    warnings.push("OPENROUTER_API_KEY not set - OpenRouter features will be limited")
  }

  if (!env.OLLAMA_BASE_URL) {
    warnings.push("OLLAMA_BASE_URL not set - will use default localhost:11434")
  }

  // Check for encryption key in production
  if (env.NODE_ENV === "production" && !env.ENCRYPTION_KEY) {
    missing.push("ENCRYPTION_KEY is required in production")
  }

  return {
    isValid: missing.length === 0,
    missing,
    warnings,
  }
}

export function logEnvStatus(): void {
  const status = checkCriticalEnvVars()

  console.log("🔧 Environment Configuration Status:")
  console.log(`   Mode: ${env.NODE_ENV}`)

  if (env.DATABASE_URL || env.POSTGRES_URL) {
    console.log("✅ Database: Configured")
  } else {
    console.log("⚠️  Database: Using local SQLite")
  }

  if (env.OPENROUTER_API_KEY) {
    console.log("✅ OpenRouter: Configured")
  } else {
    console.log("⚠️  OpenRouter: Not configured")
  }

  if (env.OLLAMA_BASE_URL) {
    console.log(`✅ Ollama: ${env.OLLAMA_BASE_URL}`)
  } else {
    console.log("⚠️  Ollama: Using default localhost:11434")
  }

  if (status.missing.length > 0) {
    console.log("❌ Missing critical variables:")
    status.missing.forEach((variable) => console.log(`   - ${variable}`))
  }

  if (status.warnings.length > 0) {
    console.log("⚠️  Warnings:")
    status.warnings.forEach((warning) => console.log(`   - ${warning}`))
  }

  if (status.isValid) {
    console.log("✅ Environment configuration is valid")
  }
}

export function validateDatabaseConfig(): {
  isValid: boolean
  type: "neon" | "local"
  url?: string
  error?: string
} {
  const databaseUrl = env.DATABASE_URL || env.POSTGRES_URL

  if (databaseUrl) {
    try {
      new URL(databaseUrl)
      return {
        isValid: true,
        type: "neon",
        url: databaseUrl,
      }
    } catch (error) {
      return {
        isValid: false,
        type: "neon",
        error: "Invalid database URL format",
      }
    }
  }

  return {
    isValid: true,
    type: "local",
  }
}

export function getPublicEnvVars() {
  return {
    NODE_ENV: env.NODE_ENV,
    VERCEL_URL: env.VERCEL_URL,
    NEXT_PUBLIC_SITE_URL: env.NEXT_PUBLIC_SITE_URL,
  }
}

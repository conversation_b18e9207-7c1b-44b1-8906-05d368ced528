"use client";

import { useState, useEffect } from "react";
import { MindMapNode } from "@/components/mind-map-node";
import { ConnectionLine } from "@/components/connection-line";
import useMindMapStore from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Save, Loader2, FileText } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

export function MindMapCanvas() {
  const {
    nodes,
    connections,
    selectedNodeId,
    selectNode,
    addConnection,
    updateNode,
    deleteNode,
  } = useMindMapStore();

  const [connectingFromId, setConnectingFromId] = useState<string | null>(null);
  const [dragging, setDragging] = useState<{ id: string; offsetX: number; offsetY: number } | null>(null);
  const [resizing, setResizing] = useState<{ id: string; startX: number; startY: number; startWidth: number } | null>(null);

  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  const handleConnectionStart = (nodeId: string) => {
    setConnectingFromId(nodeId);
  };

  const handleConnectionEnd = (targetId: string) => {
    if (connectingFromId && connectingFromId !== targetId) {
      addConnection(connectingFromId, targetId);
    }
    setConnectingFromId(null);
  };

  const handleNodeMouseDown = (e: React.MouseEvent, nodeId: string) => {
    const node = nodes.find((n) => n.id === nodeId);
    if (!node) return;
    setDragging({ id: nodeId, offsetX: e.clientX - node.x, offsetY: e.clientY - node.y });
  };

  const handleResizeStart = (e: React.MouseEvent, nodeId: string) => {
    const node = nodes.find((n) => n.id === nodeId);
    if (!node) return;
    const widthMap = { small: 120, medium: 160, large: 200 } as any;
    setResizing({ id: nodeId, startX: e.clientX, startY: e.clientY, startWidth: widthMap[node.size ?? "medium"] });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (dragging) {
      updateNodePosition(dragging.id, e.clientX - dragging.offsetX, e.clientY - dragging.offsetY);
    } else if (resizing) {
      const dx = e.clientX - resizing.startX;
      const newWidth = Math.max(80, resizing.startWidth + dx);
      const size = newWidth < 140 ? "small" : newWidth < 180 ? "medium" : "large";
      updateNode(resizing.id, { size });
    } else if (isPanning) {
       setPan({ x: e.clientX - panStart.x, y: e.clientY - panStart.y });
    }
  };

  const handleMouseUp = () => {
    setDragging(null);
    setResizing(null);
    setIsPanning(false);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom((prev) => Math.max(0.2, Math.min(3, prev * delta)));
  };

  const handleBackgroundMouseDown = (e: React.MouseEvent) => {
    // start panning if not on node
    if (e.target === e.currentTarget) {
      setIsPanning(true);
      setPanStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
    }
  };

  const updateNodePosition = (id: string, x: number, y: number) => {
    useMindMapStore.getState().updateNode(id, { x, y });
  };

  const handleNodeClick = (nodeId: string) => {
    if (connectingFromId) {
      if (connectingFromId !== nodeId) {
        addConnection(connectingFromId, nodeId);
      }
      setConnectingFromId(null);
    } else {
      selectNode(nodeId);
    }
  };

  const handleDeleteNode = (nodeId: string) => {
    deleteNode(nodeId);
  };

  const saveMap = async () => {
    if (saving) return;
    setSaving(true);
    try {
      const firstTitle = nodes[0]?.content?.substring(0,60) || "Mapa Sem Título";
      const currentId = useMindMapStore.getState().mindMapId;
      const response = await fetch("/api/mindmaps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: currentId,
          titulo: firstTitle,
          dados: { 
            nodes: nodes.map(node => ({
              id: node.id,
              content: node.content,
              x: node.x,
              y: node.y,
              level: node.level,
              color: node.color,
              size: node.size,
              icon: node.icon,
              style: node.style
            })),
            connections: connections.map(conn => ({
              from: conn.from,
              to: conn.to,
              color: conn.color,
              style: conn.style
            }))
          },
        }),
      });
      if (!response.ok) {
        console.error("Erro ao salvar mapa:", await response.text());
        toast({ title: "Erro", description: "Falha ao salvar mapa." });
      } else {
        const data = await response.json();
        if (data.id) {
          useMindMapStore.getState().setMindMapId(String(data.id));
        }
        toast({ title: "Mapa salvo", description: "Todas as alterações foram salvas." });
      }
    } catch (err) {
      console.error("Falha ao salvar mapa", err);
      toast({ title: "Erro", description: "Falha ao salvar mapa." });
    }
    setSaving(false);
  };

  const { toast } = useToast();
  const [saving, setSaving] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [markdown, setMarkdown] = useState<string | null>(null);

  // Geração de markdown a partir dos nós
  const handleGenerateMarkdown = async () => {
    if (generating) return;
    setGenerating(true);
    try {
      const { mindMapId } = useMindMapStore.getState();
      if (!mindMapId) {
        console.error("mindMapId indefinido");
        return;
      }

      const res = await fetch(`/api/mindmaps/${mindMapId}/markdown`);
      const data = await res.json();
      if (data.success) {
        setMarkdown(data.markdown);
      } else {
        console.error("Falha na geração de markdown", data.error);
      }
    } catch (err) {
      console.error("Erro ao gerar texto", err);
    } finally {
      setGenerating(false);
    }
  };

  // Auto-salvar após interações
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (nodes.length > 0) {
        saveMap();
      }
    }, 2000); // 2 segundos de delay

    return () => clearTimeout(timeoutId);
  }, [nodes, connections]);

  return (
    <div className="w-full h-full relative bg-white">
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <Button onClick={saveMap} size="sm" variant="outline" disabled={saving}>
          {saving ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />} 
          {saving ? "Salvando..." : "Salvar"}
        </Button>

        <Button onClick={handleGenerateMarkdown} size="sm" variant="outline" disabled={generating}>
          {generating ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <FileText className="w-4 h-4 mr-2" />
          )}
          {generating ? "Gerando..." : "Gerar texto"}
        </Button>
      </div>
      {markdown && (
        <Dialog open onOpenChange={() => setMarkdown(null)}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Texto Gerado (Markdown)</DialogTitle>
            </DialogHeader>
            <Textarea
              className="h-96 font-mono whitespace-pre"
              value={markdown}
              onChange={(e) => setMarkdown(e.target.value)}
            />
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  const blob = new Blob([markdown!], { type: "text/markdown" });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement("a");
                  a.href = url;
                  a.download = "mindmap.md";
                  a.click();
                  URL.revokeObjectURL(url);
                }}
              >
                Baixar .md
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      <svg className="w-full h-full" onMouseMove={handleMouseMove} onMouseUp={handleMouseUp} onWheel={handleWheel} onMouseDown={handleBackgroundMouseDown} onClick={() => setConnectingFromId(null)}>
        <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
          {connections.map((connection) => {
            const fromNode = nodes.find((n) => n.id === connection.from);
            const toNode = nodes.find((n) => n.id === connection.to);

            if (!fromNode || !toNode) return null;

            return (
              <ConnectionLine
                key={`${connection.from}-${connection.to}`}
                from={{ x: fromNode.x, y: fromNode.y }}
                to={{ x: toNode.x, y: toNode.y }}
                type="manual"
                strength={1}
              />
            );
          })}

          {nodes.map((node) => (
            <MindMapNode
              key={node.id}
              node={node}
              isSelected={selectedNodeId === node.id}
              onClick={() => handleNodeClick(node.id)}
              isConnecting={!!connectingFromId}
              onDragStart={(e) => handleNodeMouseDown(e, node.id)}
              onDelete={() => handleDeleteNode(node.id)}
              onConnectionStart={() => handleConnectionStart(node.id)}
              onConnectionEnd={() => handleConnectionEnd(node.id)}
              onResizeStart={(e) => handleResizeStart(e, node.id)}
            />
          ))}
        </g>
      </svg>
    </div>
  );
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Check, ChevronRight, ChevronLeft, Globe, Database, Brain, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useTranslation, setLanguage } from "@/lib/i18n"

interface SetupWizardProps {
  onComplete: () => void
}

export function SetupWizard({ onComplete }: SetupWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [config, setConfig] = useState({
    language: "pt",
    database_type: "neon",
    database_url: "",
    llm_provider: "openrouter",
    openrouter_api_key: "",
    ollama_base_url: "http://localhost:11434",
    default_model: "openai/gpt-4o-mini",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState({
    database: false,
    ai: false,
  })
  const { toast } = useToast()
  const { t } = useTranslation()

  const steps = [
    {
      title: t.setupWizard.language.title,
      description: t.setupWizard.language.description,
      icon: Globe,
    },
    {
      title: t.setupWizard.database.title,
      description: t.setupWizard.database.description,
      icon: Database,
    },
    {
      title: t.setupWizard.ai.title,
      description: t.setupWizard.ai.description,
      icon: Brain,
    },
    {
      title: t.setupWizard.complete.title,
      description: t.setupWizard.complete.description,
      icon: CheckCircle,
    },
  ]

useEffect(() => {
    // Force Portuguese
    setLanguage("pt")
    setConfig((prev) => ({ ...prev, language: "pt" }))
  }, [])
  const testDatabaseConnection = async () => {
    if (config.database_type === "neon" && !config.database_url) {
      toast({
        title: "Erro",
        description: "URL do banco de dados é obrigatória para Neon",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch("/api/test-connection", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: "database",
          database_type: config.database_type,
          database_url: config.database_url,
        }),
      })

      const result = await response.json()
      if (result.success) {
        setTestResults((prev) => ({ ...prev, database: true }))
        toast({
          title: "Sucesso",
          description: t.setupWizard.database.connectionSuccess,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: `${t.setupWizard.database.connectionError}: ${error}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testAIConnection = async () => {
    if (config.llm_provider === "openrouter" && !config.openrouter_api_key) {
      toast({
        title: "Erro",
        description: "Chave da API OpenRouter é obrigatória",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch("/api/test-connection", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: "ai",
          llm_provider: config.llm_provider,
          openrouter_api_key: config.openrouter_api_key,
          ollama_base_url: config.ollama_base_url,
        }),
      })

      const result = await response.json()
      if (result.success) {
        setTestResults((prev) => ({ ...prev, ai: true }))
        toast({
          title: "Sucesso",
          description: t.setupWizard.ai.connectionSuccess,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: `${t.setupWizard.ai.connectionError}: ${error}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const completeSetup = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/config", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...config,
          language: "pt", // Force Portuguese
          setup_completed: true,
        }),
      })

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: t.setupWizard.complete.description,
        })
        onComplete()
      } else {
        throw new Error("Falha ao salvar configuração")
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: `Erro ao completar configuração: ${error}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Language
        return true
      case 1: // Database
        return config.database_type === "sqlite" || (config.database_type === "neon" && config.database_url)
      case 2: // AI
        return (
          (config.llm_provider === "openrouter" && config.openrouter_api_key) ||
          (config.llm_provider === "ollama" && config.ollama_base_url)
        )
      case 3: // Complete
        return true
      default:
        return false
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 0: // Language Selection
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card
                className={`cursor-pointer transition-all ${
                  config.language === "pt" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => {
                  setConfig((prev) => ({ ...prev, language: "pt" }))
                  setLanguage("pt")
                }}
              >
                <CardContent className="p-6 text-center">
                  <div className="text-2xl mb-2">🇧🇷</div>
                  <h3 className="font-semibold">{t.setupWizard.language.portuguese}</h3>
                  {config.language === "pt" && <Check className="h-5 w-5 text-blue-500 mx-auto mt-2" />}
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  config.language === "en" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => {
                  setConfig((prev) => ({ ...prev, language: "en" }))
                  setLanguage("en")
                }}
              >
                <CardContent className="p-6 text-center">
                  <div className="text-2xl mb-2">🇺🇸</div>
                  <h3 className="font-semibold">{t.setupWizard.language.english}</h3>
                  {config.language === "en" && <Check className="h-5 w-5 text-blue-500 mx-auto mt-2" />}
                </CardContent>
              </Card>
            </div>
          </div>
        )

      case 1: // Database Configuration
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card
                className={`cursor-pointer transition-all ${
                  config.database_type === "sqlite" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => setConfig((prev) => ({ ...prev, database_type: "sqlite" }))}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{t.setupWizard.database.local}</h3>
                    {config.database_type === "sqlite" && <Check className="h-5 w-5 text-blue-500" />}
                  </div>
                  <p className="text-sm text-gray-600">{t.setupWizard.database.localDescription}</p>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  config.database_type === "neon" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => setConfig((prev) => ({ ...prev, database_type: "neon" }))}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{t.setupWizard.database.neon}</h3>
                    {config.database_type === "neon" && <Check className="h-5 w-5 text-blue-500" />}
                  </div>
                  <p className="text-sm text-gray-600">{t.setupWizard.database.neonDescription}</p>
                </CardContent>
              </Card>
            </div>

            {config.database_type === "neon" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">{t.setupWizard.database.connectionString}</label>
                  <Input
                    type="password"
                    placeholder={t.setupWizard.database.connectionStringPlaceholder}
                    value={config.database_url}
                    onChange={(e) => setConfig((prev) => ({ ...prev, database_url: e.target.value }))}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={testDatabaseConnection}
                    disabled={isLoading || !config.database_url}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        {t.setupWizard.database.testing}
                      </>
                    ) : (
                      t.setupWizard.database.testConnection
                    )}
                  </Button>
                  {testResults.database && (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <Check className="h-3 w-3 mr-1" />
                      Conectado
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        )

      case 2: // AI Configuration
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card
                className={`cursor-pointer transition-all ${
                  config.llm_provider === "openrouter" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => setConfig((prev) => ({ ...prev, llm_provider: "openrouter" }))}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{t.setupWizard.ai.openrouter}</h3>
                    {config.llm_provider === "openrouter" && <Check className="h-5 w-5 text-blue-500" />}
                  </div>
                  <p className="text-sm text-gray-600">{t.setupWizard.ai.openrouterDescription}</p>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  config.llm_provider === "ollama" ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => setConfig((prev) => ({ ...prev, llm_provider: "ollama" }))}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{t.setupWizard.ai.ollama}</h3>
                    {config.llm_provider === "ollama" && <Check className="h-5 w-5 text-blue-500" />}
                  </div>
                  <p className="text-sm text-gray-600">{t.setupWizard.ai.ollamaDescription}</p>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-4">
              {config.llm_provider === "openrouter" && (
                <div>
                  <label className="block text-sm font-medium mb-2">{t.setupWizard.ai.apiKey}</label>
                  <Input
                    type="password"
                    placeholder={t.setupWizard.ai.apiKeyPlaceholder}
                    value={config.openrouter_api_key}
                    onChange={(e) => setConfig((prev) => ({ ...prev, openrouter_api_key: e.target.value }))}
                  />
                </div>
              )}

              {config.llm_provider === "ollama" && (
                <div>
                  <label className="block text-sm font-medium mb-2">{t.setupWizard.ai.baseUrl}</label>
                  <Input
                    placeholder={t.setupWizard.ai.baseUrlPlaceholder}
                    value={config.ollama_base_url}
                    onChange={(e) => setConfig((prev) => ({ ...prev, ollama_base_url: e.target.value }))}
                  />
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={testAIConnection}
                  disabled={
                    isLoading ||
                    (config.llm_provider === "openrouter" && !config.openrouter_api_key) ||
                    (config.llm_provider === "ollama" && !config.ollama_base_url)
                  }
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      {t.setupWizard.ai.testing}
                    </>
                  ) : (
                    t.setupWizard.ai.testConnection
                  )}
                </Button>
                {testResults.ai && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <Check className="h-3 w-3 mr-1" />
                    Conectado
                  </Badge>
                )}
              </div>
            </div>
          </div>
        )

      case 3: // Complete
        return (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{t.setupWizard.complete.title}</h2>
              <p className="text-gray-600">{t.setupWizard.complete.description}</p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>{t.setupWizard.complete.summary}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">Idioma:</span>
                  <span>🇧🇷 Português (Brasil)</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">{t.setupWizard.complete.database}</span>
                  <span>{config.database_type === "sqlite" ? "SQLite Local" : "Neon Database"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">{t.setupWizard.complete.aiProvider}</span>
                  <span>{config.llm_provider === "openrouter" ? "OpenRouter" : "Ollama"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">{t.setupWizard.complete.defaultModel}</span>
                  <span>{config.default_model}</span>
                </div>
              </CardContent>
            </Card>

            <Button onClick={completeSetup} disabled={isLoading} className="w-full" size="lg">
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Finalizando...
                </>
              ) : (
                t.setupWizard.complete.startUsing
              )}
            </Button>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Brain className="h-10 w-10 text-blue-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Mind Map IA</h1>
          </div>
          <CardTitle className="text-xl">{t.setupWizard.title}</CardTitle>
          <CardDescription>{t.setupWizard.subtitle}</CardDescription>
        </CardHeader>

        <CardContent>
          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {steps.map((step, index) => {
              const StepIcon = step.icon
              const isActive = index === currentStep
              const isCompleted = index < currentStep

              return (
                <div key={index} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isActive
                        ? "border-blue-500 bg-blue-500 text-white"
                        : isCompleted
                          ? "border-green-500 bg-green-500 text-white"
                          : "border-gray-300 bg-white text-gray-400"
                    }`}
                  >
                    {isCompleted ? <Check className="h-5 w-5" /> : <StepIcon className="h-5 w-5" />}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 ml-2 ${isCompleted ? "bg-green-500" : "bg-gray-300"}`} />
                  )}
                </div>
              )
            })}
          </div>

          {/* Step Content */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{steps[currentStep].title}</h2>
              <p className="text-gray-600">{steps[currentStep].description}</p>
            </div>
            {renderStep()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={prevStep} disabled={currentStep === 0}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              {t.setupWizard.buttons.previous}
            </Button>

            {currentStep < steps.length - 1 ? (
              <Button onClick={nextStep} disabled={!canProceed()}>
                {t.setupWizard.buttons.next}
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={completeSetup} disabled={isLoading || !canProceed()}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Finalizando...
                  </>
                ) : (
                  t.setupWizard.buttons.finish
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

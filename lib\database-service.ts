import { getDatabase, saveConfig, getConfig, saveMindMap, getMindMaps, getMindMap, deleteMindMap } from "./database"
import { SecurityManager } from "./security"
import { z } from "zod"

const mindMapSchema = z.object({
  nome: z.string().min(1).max(255),
  descricao: z.string().optional(),
  dados: z.object({
    nodes: z.array(z.any()),
    connections: z.array(z.any()),
    settings: z.any().optional(),
  }),
})

export class BackupService {
  static async createBackup(): Promise<{
    success: boolean
    data?: any
    error?: string
  }> {
    try {
      const mindMaps = await getMindMaps()
      const config = await getConfig()

      const backup = {
        version: "1.0",
        timestamp: new Date().toISOString(),
        data: {
          mindMaps,
          config,
        },
        checksum: SecurityManager.generateSecureToken(),
      }

      return {
        success: true,
        data: backup,
      }
    } catch (error) {
      console.error("Backup creation failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async restoreBackup(backupData: any): Promise<{
    success: boolean
    restored?: { mindMaps: number; configs: number }
    error?: string
  }> {
    try {
      if (!backupData.version || !backupData.data) {
        throw new Error("Invalid backup format")
      }

      const { mindMaps, config } = backupData.data
      let restoredMindMaps = 0
      let restoredConfigs = 0

      // Restore configurations
      if (config) {
        await saveConfig(config)
        restoredConfigs = Object.keys(config).length
      }

      // Restore mind maps
      if (mindMaps && Array.isArray(mindMaps)) {
        for (const mindMap of mindMaps) {
          try {
            await saveMindMap(mindMap)
            restoredMindMaps++
          } catch (error) {
            console.warn("Failed to restore mind map:", mindMap.nome, error)
          }
        }
      }

      return {
        success: true,
        restored: {
          mindMaps: restoredMindMaps,
          configs: restoredConfigs,
        },
      }
    } catch (error) {
      console.error("Backup restoration failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static validateBackup(backupData: any): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!backupData) {
      errors.push("Backup data is empty")
      return { isValid: false, errors }
    }

    if (!backupData.version) {
      errors.push("Backup version is missing")
    }

    if (!backupData.timestamp) {
      errors.push("Backup timestamp is missing")
    }

    if (!backupData.data) {
      errors.push("Backup data is missing")
    } else {
      if (backupData.data.mindMaps && !Array.isArray(backupData.data.mindMaps)) {
        errors.push("Mind maps data is not an array")
      }

      if (backupData.data.config && typeof backupData.data.config !== "object") {
        errors.push("Config data is not an object")
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}

export class MindMapService {
  static async createMindMap(data: {
    nome: string
    descricao?: string
    dados: { nodes: any[]; connections: any[] }
    senha?: string
  }): Promise<{ success: boolean; id?: string; error?: string }> {
    try {
      // Validate input
      const validatedData = mindMapSchema.parse(data)

      // Sanitize input
      const sanitizedData = {
        ...validatedData,
        nome: SecurityManager.sanitizeInput(validatedData.nome),
        descricao: validatedData.descricao ? SecurityManager.sanitizeInput(validatedData.descricao) : undefined,
      }

      // Hash password if provided
      let passwordHash: string | undefined
      if (data.senha) {
        passwordHash = await SecurityManager.hashPassword(data.senha)
      }

      const id = await saveMindMap(sanitizedData, passwordHash)

      return {
        success: true,
        id,
      }
    } catch (error) {
      console.error("Mind map creation failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async updateMindMap(
    id: string,
    data: Partial<{
      nome: string
      descricao: string
      dados: { nodes: any[]; connections: any[] }
    }>,
    senha?: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get existing mind map to verify password if needed
      const existing = await getMindMap(id, senha ? await SecurityManager.hashPassword(senha) : undefined)

      if (!existing) {
        throw new Error("Mind map not found or invalid password")
      }

      // Sanitize input
      const sanitizedData: any = {}
      if (data.nome) sanitizedData.nome = SecurityManager.sanitizeInput(data.nome)
      if (data.descricao) sanitizedData.descricao = SecurityManager.sanitizeInput(data.descricao)
      if (data.dados) sanitizedData.dados = data.dados

      await saveMindMap({ ...existing, ...sanitizedData })

      return { success: true }
    } catch (error) {
      console.error("Mind map update failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async deleteMindMapSecure(id: string, senha?: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Verify access first
      const mindMap = await getMindMap(id, senha ? await SecurityManager.hashPassword(senha) : undefined)

      if (!mindMap) {
        throw new Error("Mind map not found or invalid password")
      }

      await deleteMindMap(id)

      return { success: true }
    } catch (error) {
      console.error("Mind map deletion failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async searchMindMaps(query: string): Promise<any[]> {
    try {
      const allMindMaps = await getMindMaps()

      if (!query.trim()) {
        return allMindMaps
      }

      const searchTerm = query.toLowerCase()

      return allMindMaps.filter(
        (mindMap) =>
          mindMap.nome.toLowerCase().includes(searchTerm) ||
          (mindMap.descricao && mindMap.descricao.toLowerCase().includes(searchTerm)),
      )
    } catch (error) {
      console.error("Mind map search failed:", error)
      return []
    }
  }
}

export class ConfigService {
  static async updateLanguage(language: "pt" | "en"): Promise<{ success: boolean; error?: string }> {
    try {
      await saveConfig({ language })
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async updateTheme(theme: "light" | "dark"): Promise<{ success: boolean; error?: string }> {
    try {
      await saveConfig({ theme })
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  static async updateAIProvider(provider: {
    llm_provider: "openrouter" | "ollama"
    openrouter_api_key?: string
    ollama_base_url?: string
    default_model?: string
  }): Promise<{ success: boolean; error?: string }> {
    try {
      await saveConfig(provider)
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }
}

export class DatabaseHealthService {
  static async checkHealth(): Promise<{
    status: "healthy" | "degraded" | "unhealthy"
    checks: Array<{ name: string; status: "pass" | "fail"; message: string }>
  }> {
    const checks = []

    // Test database connection
    try {
      const db = getDatabase()
      await db`SELECT 1`
      checks.push({
        name: "Database Connection",
        status: "pass" as const,
        message: "Database connection successful",
      })
    } catch (error) {
      checks.push({
        name: "Database Connection",
        status: "fail" as const,
        message: `Database connection failed: ${error}`,
      })
    }

    // Test configuration access
    try {
      await getConfig()
      checks.push({
        name: "Configuration Access",
        status: "pass" as const,
        message: "Configuration access successful",
      })
    } catch (error) {
      checks.push({
        name: "Configuration Access",
        status: "fail" as const,
        message: `Configuration access failed: ${error}`,
      })
    }

    // Test mind maps access
    try {
      await getMindMaps()
      checks.push({
        name: "Mind Maps Access",
        status: "pass" as const,
        message: "Mind maps access successful",
      })
    } catch (error) {
      checks.push({
        name: "Mind Maps Access",
        status: "fail" as const,
        message: `Mind maps access failed: ${error}`,
      })
    }

    const failedChecks = checks.filter((check) => check.status === "fail")
    const status =
      failedChecks.length === 0 ? "healthy" : failedChecks.length < checks.length ? "degraded" : "unhealthy"

    return { status, checks }
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { testDatabaseConnection } from "@/lib/database"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, url } = body

    if (!type) {
      return NextResponse.json({ success: false, error: "Database type is required" }, { status: 400 })
    }

    if (type === "neon" && !url) {
      return NextResponse.json({ success: false, error: "Database URL is required for Neon database" }, { status: 400 })
    }

    const result = await testDatabaseConnection(type, url)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Database test error:", error)
    return NextResponse.json({ success: false, error: "Database test failed" }, { status: 500 })
  }
}

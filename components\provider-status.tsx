"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Cloud, Server, AlertCircle, CheckCircle, RefreshCw } from "lucide-react"

interface ProviderStatusProps {
  provider: "openrouter" | "ollama"
  config: {
    apiKey?: string
    baseUrl?: string
  }
}

export function ProviderStatus({ provider, config }: ProviderStatusProps) {
  const [status, setStatus] = useState<"checking" | "connected" | "error" | "idle">("idle")
  const [message, setMessage] = useState("")
  const [modelCount, setModelCount] = useState(0)

  const checkConnection = async () => {
    setStatus("checking")
    try {
      const response = await fetch("/api/llm/test-connection", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ provider, config }),
      })

      const result = await response.json()
      setStatus(result.success ? "connected" : "error")
      setMessage(result.message)
      setModelCount(result.modelCount || 0)
    } catch (error) {
      setStatus("error")
      setMessage("Erro de conexão")
    }
  }

  useEffect(() => {
    if ((provider === "openrouter" && config.apiKey) || (provider === "ollama" && config.baseUrl)) {
      checkConnection()
    } else {
      setStatus("idle")
      setMessage("")
    }
  }, [provider, config.apiKey, config.baseUrl])

  const getStatusIcon = () => {
    switch (status) {
      case "checking":
        return <RefreshCw className="w-4 h-4 animate-spin" />
      case "connected":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return provider === "openrouter" ? <Cloud className="w-4 h-4" /> : <Server className="w-4 h-4" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case "connected":
        return "bg-green-100 text-green-800 border-green-200"
      case "error":
        return "bg-red-100 text-red-800 border-red-200"
      case "checking":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Badge className={`${getStatusColor()} border`}>
          <div className="flex items-center gap-1">
            {getStatusIcon()}
            <span>{provider === "openrouter" ? "OpenRouter" : "Ollama"}</span>
          </div>
        </Badge>

        {status !== "checking" && (
          <Button variant="outline" size="sm" onClick={checkConnection}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Verificar
          </Button>
        )}
      </div>

      {message && (
        <div className="text-sm">
          <p className={status === "error" ? "text-red-600" : "text-green-600"}>{message}</p>
          {status === "connected" && modelCount > 0 && (
            <p className="text-gray-500 text-xs mt-1">{modelCount} modelos disponíveis</p>
          )}
        </div>
      )}
    </div>
  )
}

import { NextResponse } from "next/server"
import { checkDatabaseHealth } from "@/lib/database"

export async function GET() {
  try {
    const health = await checkDatabaseHealth()

    return NextResponse.json({
      success: true,
      database: health,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Erro no health check:", error)
    return NextResponse.json(
      {
        success: false,
        database: { healthy: false, message: `Erro: ${error}` },
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}

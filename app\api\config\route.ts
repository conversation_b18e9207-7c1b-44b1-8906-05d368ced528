import { type NextRequest, NextResponse } from "next/server"
import { getConfig, saveConfig, initializeDatabase } from "@/lib/database"
import { getUserFromRequest } from "@/lib/middleware"

export async function GET() {
  try {
    await initializeDatabase()
    const config = await getConfig()
    return NextResponse.json(config)
  } catch (error) {
    console.error("Error getting config:", error)
    return NextResponse.json({ error: "Failed to get configuration" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const user = getUserFromRequest(request)
    if (!user || !user.isAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: "Acesso negado. Apenas administradores podem alterar configurações."
        },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Force Portuguese language
    body.language = "pt"

    await initializeDatabase()
    await saveConfig(body)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error saving config:", error)
    return NextResponse.json({ error: "Failed to save configuration" }, { status: 500 })
  }
}

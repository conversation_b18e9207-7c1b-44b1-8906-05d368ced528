#!/bin/bash

# 🚀 Script de Configuração Automática das Variáveis de Ambiente no Vercel
# Este script configura todas as variáveis necessárias para a aplicação Mind Map

set -e

echo "🔧 Configurando Variáveis de Ambiente no Vercel..."
echo "=================================================="

# Verificar se o Vercel CLI está instalado
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI não encontrado. Instalando..."
    npm install -g vercel
fi

# Fazer login no Vercel (se necessário)
echo "🔐 Verificando autenticação no Vercel..."
if ! vercel whoami &> /dev/null; then
    echo "Por favor, faça login no Vercel:"
    vercel login
fi

echo ""
echo "📋 Configurando variáveis obrigatórias..."

# NEON_DATABASE_URL
echo ""
echo "1️⃣  NEON_DATABASE_URL"
echo "   Formato: postgresql://user:<EMAIL>/database?sslmode=require"
echo "   Obtenha em: https://neon.tech"
read -p "   Digite a URL do banco Neon: " NEON_URL
if [ -n "$NEON_URL" ]; then
    echo "$NEON_URL" | vercel env add NEON_DATABASE_URL production
    echo "   ✅ NEON_DATABASE_URL configurado"
else
    echo "   ⚠️  NEON_DATABASE_URL não configurado"
fi

# SESSION_SECRET
echo ""
echo "2️⃣  SESSION_SECRET"
echo "   Gerando chave aleatória de 32 caracteres..."
SESSION_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
echo "$SESSION_SECRET" | vercel env add SESSION_SECRET production
echo "   ✅ SESSION_SECRET configurado: ${SESSION_SECRET:0:8}..."

# ENCRYPTION_KEY
echo ""
echo "3️⃣  ENCRYPTION_KEY"
echo "   Gerando chave aleatória de 32 caracteres..."
ENCRYPTION_KEY=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
echo "$ENCRYPTION_KEY" | vercel env add ENCRYPTION_KEY production
echo "   ✅ ENCRYPTION_KEY configurado: ${ENCRYPTION_KEY:0:8}..."

echo ""
echo "🔧 Configurando variáveis opcionais..."

# VERCEL_REGION
echo ""
echo "4️⃣  VERCEL_REGION"
read -p "   Digite a região (padrão: us-east-1): " REGION
REGION=${REGION:-us-east-1}
echo "$REGION" | vercel env add VERCEL_REGION production
echo "   ✅ VERCEL_REGION configurado: $REGION"

# MAX_CONNECTIONS
echo ""
echo "5️⃣  MAX_CONNECTIONS"
read -p "   Digite o máximo de conexões (padrão: 10): " MAX_CONN
MAX_CONN=${MAX_CONN:-10}
echo "$MAX_CONN" | vercel env add MAX_CONNECTIONS production
echo "   ✅ MAX_CONNECTIONS configurado: $MAX_CONN"

# CONNECTION_TIMEOUT
echo ""
echo "6️⃣  CONNECTION_TIMEOUT"
read -p "   Digite o timeout de conexão em ms (padrão: 10000): " CONN_TIMEOUT
CONN_TIMEOUT=${CONN_TIMEOUT:-10000}
echo "$CONN_TIMEOUT" | vercel env add CONNECTION_TIMEOUT production
echo "   ✅ CONNECTION_TIMEOUT configurado: $CONN_TIMEOUT ms"

# QUERY_TIMEOUT
echo ""
echo "7️⃣  QUERY_TIMEOUT"
read -p "   Digite o timeout de query em ms (padrão: 30000): " QUERY_TIMEOUT
QUERY_TIMEOUT=${QUERY_TIMEOUT:-30000}
echo "$QUERY_TIMEOUT" | vercel env add QUERY_TIMEOUT production
echo "   ✅ QUERY_TIMEOUT configurado: $QUERY_TIMEOUT ms"

echo ""
echo "🚀 Fazendo deploy da aplicação..."
vercel --prod

echo ""
echo "✅ Configuração concluída com sucesso!"
echo "=================================================="
echo ""
echo "🔍 Para verificar se tudo está funcionando:"
echo "   curl https://your-app.vercel.app/api/health"
echo ""
echo "📊 Para verificar o banco de dados:"
echo "   curl https://your-app.vercel.app/api/database/health"
echo ""
echo "🎉 Sua aplicação Mind Map está pronta para uso!"

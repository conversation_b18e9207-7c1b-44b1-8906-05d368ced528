"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Share2, Trash2, Mail, Eye, Edit, Loader2 } from "lucide-react"

interface ShareModalProps {
  isOpen: boolean
  onClose: () => void
  mindMapId: string
  mindMapTitle: string
}

interface Share {
  id: string
  shared_with_email: string
  shared_with_name: string
  permission: 'view' | 'edit'
  created_at: string
}

export function ShareModal({ isOpen, onClose, mindMapId, mindMapTitle }: ShareModalProps) {
  const [email, setEmail] = useState("")
  const [permission, setPermission] = useState<'view' | 'edit'>('view')
  const [shares, setShares] = useState<Share[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingShares, setIsLoadingShares] = useState(false)
  const { toast } = useToast()

  const loadShares = async () => {
    setIsLoadingShares(true)
    try {
      const response = await fetch(`/api/mindmaps/${mindMapId}/share`)
      if (response.ok) {
        const data = await response.json()
        setShares(data.shares || [])
      }
    } catch (error) {
      console.error('Error loading shares:', error)
    } finally {
      setIsLoadingShares(false)
    }
  }

  useEffect(() => {
    if (isOpen && mindMapId) {
      loadShares()
    }
  }, [isOpen, mindMapId])

  const handleShare = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      toast({
        title: "Erro",
        description: "Email é obrigatório",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/mindmaps/${mindMapId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: email.trim(), permission })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Sucesso!",
          description: data.message,
        })
        setEmail("")
        setPermission('view')
        loadShares() // Reload shares
      } else {
        toast({
          title: "Erro",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Share error:', error)
      toast({
        title: "Erro",
        description: "Erro interno do servidor",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveShare = async (shareId: string) => {
    try {
      const response = await fetch(`/api/mindmaps/${mindMapId}/share?shareId=${shareId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Sucesso!",
          description: data.message,
        })
        loadShares() // Reload shares
      } else {
        toast({
          title: "Erro",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Remove share error:', error)
      toast({
        title: "Erro",
        description: "Erro interno do servidor",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Compartilhar Mapa Mental
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Mind Map Info */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="font-medium text-sm text-gray-900">{mindMapTitle}</p>
            <p className="text-xs text-gray-500">Compartilhe este mapa mental com outros usuários</p>
          </div>

          {/* Share Form */}
          <form onSubmit={handleShare} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email do usuário</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="permission">Permissão</Label>
              <Select value={permission} onValueChange={(value: 'view' | 'edit') => setPermission(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="view">
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Visualizar apenas
                    </div>
                  </SelectItem>
                  <SelectItem value="edit">
                    <div className="flex items-center gap-2">
                      <Edit className="h-4 w-4" />
                      Visualizar e editar
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Compartilhando...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  Compartilhar
                </>
              )}
            </Button>
          </form>

          {/* Current Shares */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Compartilhado com:</h4>
              {isLoadingShares && <Loader2 className="h-4 w-4 animate-spin" />}
            </div>

            {shares.length === 0 ? (
              <p className="text-sm text-gray-500 text-center py-4">
                Nenhum compartilhamento ainda
              </p>
            ) : (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {shares.map((share) => (
                  <div key={share.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{share.shared_with_name}</p>
                      <p className="text-xs text-gray-500 truncate">{share.shared_with_email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={share.permission === 'edit' ? 'default' : 'secondary'}>
                        {share.permission === 'edit' ? (
                          <><Edit className="h-3 w-3 mr-1" />Editar</>
                        ) : (
                          <><Eye className="h-3 w-3 mr-1" />Ver</>
                        )}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveShare(share.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

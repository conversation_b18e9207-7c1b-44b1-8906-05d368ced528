import { NextRequest, NextResponse } from 'next/server'
import { setMindMapPassword, removeMindMapPassword, getMindMapById } from '@/lib/database'
import { getUserFromRequest } from '@/lib/middleware'
import { z } from 'zod'

const setPasswordSchema = z.object({
  password: z.string().min(1, 'Senha é obrigatória')
})

// Set password protection for a mind map
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    // Verify the user owns this mind map
    const mindMap = await getMindMapById(mindmapId)
    if (!mindMap || mindMap.user_id !== user.userId) {
      return NextResponse.json(
        { success: false, error: 'Mapa mental não encontrado ou sem permissão' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const validation = setPasswordSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Dados inválidos',
          details: validation.error.errors 
        },
        { status: 400 }
      )
    }

    const { password } = validation.data

    await setMindMapPassword(mindmapId, password, user.userId)

    return NextResponse.json({
      success: true,
      message: 'Senha definida com sucesso'
    })

  } catch (error: any) {
    console.error('Set mind map password error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Remove password protection from a mind map
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    // Verify the user owns this mind map
    const mindMap = await getMindMapById(mindmapId)
    if (!mindMap || mindMap.user_id !== user.userId) {
      return NextResponse.json(
        { success: false, error: 'Mapa mental não encontrado ou sem permissão' },
        { status: 404 }
      )
    }

    await removeMindMapPassword(mindmapId, user.userId)

    return NextResponse.json({
      success: true,
      message: 'Proteção por senha removida com sucesso'
    })

  } catch (error) {
    console.error('Remove mind map password error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

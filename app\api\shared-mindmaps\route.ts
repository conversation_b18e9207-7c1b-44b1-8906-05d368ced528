import { NextRequest, NextResponse } from 'next/server'
import { getSharedMindMaps } from '@/lib/database'
import { getUserFromRequest } from '@/lib/middleware'

// Get all mind maps shared with the current user
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    const sharedMindMaps = await getSharedMindMaps(user.userId)

    // Format the response to match the expected structure
    const formattedMindMaps = sharedMindMaps.map((share: any) => ({
      id: share.mindmap_id,
      titulo: share.name,
      dados: typeof share.data === 'string' ? JSON.parse(share.data) : share.data,
      criado_em: share.mindmap_created_at,
      atualizado_em: share.mindmap_updated_at,
      descricao: share.description,
      user_id: share.owner_id,
      owner_name: share.owner_name,
      owner_email: share.owner_email,
      permission: share.permission,
      shared_at: share.created_at,
      share_id: share.id
    }))

    return NextResponse.json({
      success: true,
      mindMaps: formattedMindMaps
    })

  } catch (error) {
    console.error('Get shared mind maps error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

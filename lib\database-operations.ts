import { getDatabase } from "./database"
import { generateId } from "./utils"
import type { MindMapNode, MindMapConnection, MindMap } from "../types/mindmap"

// Interface para operações de configuração
interface ConfigRepository {
  get(key: string): string | null
  set(key: string, value: string): void
  getAll(): Record<string, string>
  delete(key: string): void
}

// Interface para operações de mapas mentais
interface MindMapRepository {
  create(mindMap: Omit<MindMap, "id" | "created_at" | "updated_at">): string
  getById(id: string): MindMap | null
  getAll(): MindMap[]
  update(id: string, updates: Partial<MindMap>): boolean
  delete(id: string): boolean
  search(query: string): MindMap[]
}

// Implementação do repositório de configurações
class ConfigRepositoryImpl implements ConfigRepository {
  private db = getDatabase()

  get(key: string): string | null {
    try {
      const stmt = this.db.prepare("SELECT valor FROM configuracoes WHERE chave = ?")
      const result = stmt.get(key) as { valor: string } | undefined
      return result?.valor || null
    } catch (error) {
      console.error("Erro ao buscar configuração:", error)
      return null
    }
  }

  set(key: string, value: string): void {
    try {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO configuracoes (chave, valor, updated_at)
        VALUES (?, ?, datetime('now'))
      `)
      stmt.run(key, value)
    } catch (error) {
      console.error("Erro ao salvar configuração:", error)
      throw error
    }
  }

  getAll(): Record<string, string> {
    try {
      const stmt = this.db.prepare("SELECT chave, valor FROM configuracoes")
      const results = stmt.all() as { chave: string; valor: string }[]

      const config: Record<string, string> = {}
      results.forEach((row) => {
        config[row.chave] = row.valor
      })

      return config
    } catch (error) {
      console.error("Erro ao buscar todas as configurações:", error)
      return {}
    }
  }

  delete(key: string): void {
    try {
      const stmt = this.db.prepare("DELETE FROM configuracoes WHERE chave = ?")
      stmt.run(key)
    } catch (error) {
      console.error("Erro ao deletar configuração:", error)
      throw error
    }
  }
}

// Implementação do repositório de mapas mentais
class MindMapRepositoryImpl implements MindMapRepository {
  private db = getDatabase()

  create(mindMap: Omit<MindMap, "id" | "created_at" | "updated_at">): string {
    const id = generateId()
    const now = new Date().toISOString()

    try {
      this.db.transaction(() => {
        // Inserir mapa mental
        const mapStmt = this.db.prepare(`
          INSERT INTO mapas_mentais (id, titulo, descricao, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?)
        `)
        mapStmt.run(id, mindMap.titulo, mindMap.descricao || null, now, now)

        // Inserir nós
        if (mindMap.nodes && mindMap.nodes.length > 0) {
          const nodeStmt = this.db.prepare(`
            INSERT INTO nos (id, mapa_id, content, x, y, color, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `)

          mindMap.nodes.forEach((node) => {
            nodeStmt.run(node.id, id, node.content, node.x, node.y, node.color, now)
          })
        }

        // Inserir conexões
        if (mindMap.connections && mindMap.connections.length > 0) {
          const connStmt = this.db.prepare(`
            INSERT INTO conexoes (id, mapa_id, from_node_id, to_node_id, created_at)
            VALUES (?, ?, ?, ?, ?)
          `)

          mindMap.connections.forEach((conn) => {
            const connId = generateId()
            connStmt.run(connId, id, conn.from, conn.to, now)
          })
        }
      })()

      return id
    } catch (error) {
      console.error("Erro ao criar mapa mental:", error)
      throw error
    }
  }

  getById(id: string): MindMap | null {
    try {
      // Buscar mapa mental
      const mapStmt = this.db.prepare(`
        SELECT id, titulo, descricao, created_at, updated_at
        FROM mapas_mentais
        WHERE id = ?
      `)
      const map = mapStmt.get(id) as any

      if (!map) return null

      // Buscar nós
      const nodesStmt = this.db.prepare(`
        SELECT id, content, x, y, color
        FROM nos
        WHERE mapa_id = ?
        ORDER BY created_at
      `)
      const nodes = nodesStmt.all(id) as MindMapNode[]

      // Buscar conexões
      const connsStmt = this.db.prepare(`
        SELECT from_node_id as 'from', to_node_id as 'to'
        FROM conexoes
        WHERE mapa_id = ?
        ORDER BY created_at
      `)
      const connections = connsStmt.all(id) as MindMapConnection[]

      return {
        id: map.id,
        titulo: map.titulo,
        descricao: map.descricao,
        nodes,
        connections,
        created_at: map.created_at,
        updated_at: map.updated_at,
      }
    } catch (error) {
      console.error("Erro ao buscar mapa mental:", error)
      return null
    }
  }

  getAll(): MindMap[] {
    try {
      const stmt = this.db.prepare(`
        SELECT id, titulo, descricao, created_at, updated_at
        FROM mapas_mentais
        ORDER BY updated_at DESC
      `)
      const maps = stmt.all() as any[]

      return maps.map((map) => ({
        id: map.id,
        titulo: map.titulo,
        descricao: map.descricao,
        nodes: [],
        connections: [],
        created_at: map.created_at,
        updated_at: map.updated_at,
      }))
    } catch (error) {
      console.error("Erro ao buscar mapas mentais:", error)
      return []
    }
  }

  update(id: string, updates: Partial<MindMap>): boolean {
    try {
      const now = new Date().toISOString()

      this.db.transaction(() => {
        // Atualizar mapa mental
        if (updates.titulo || updates.descricao) {
          const mapStmt = this.db.prepare(`
            UPDATE mapas_mentais
            SET titulo = COALESCE(?, titulo),
                descricao = COALESCE(?, descricao),
                updated_at = ?
            WHERE id = ?
          `)
          mapStmt.run(updates.titulo, updates.descricao, now, id)
        }

        // Atualizar nós se fornecidos
        if (updates.nodes) {
          // Deletar nós existentes
          const deleteNodesStmt = this.db.prepare("DELETE FROM nos WHERE mapa_id = ?")
          deleteNodesStmt.run(id)

          // Inserir novos nós
          if (updates.nodes.length > 0) {
            const nodeStmt = this.db.prepare(`
              INSERT INTO nos (id, mapa_id, content, x, y, color, created_at)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `)

            updates.nodes.forEach((node) => {
              nodeStmt.run(node.id, id, node.content, node.x, node.y, node.color, now)
            })
          }
        }

        // Atualizar conexões se fornecidas
        if (updates.connections) {
          // Deletar conexões existentes
          const deleteConnsStmt = this.db.prepare("DELETE FROM conexoes WHERE mapa_id = ?")
          deleteConnsStmt.run(id)

          // Inserir novas conexões
          if (updates.connections.length > 0) {
            const connStmt = this.db.prepare(`
              INSERT INTO conexoes (id, mapa_id, from_node_id, to_node_id, created_at)
              VALUES (?, ?, ?, ?, ?)
            `)

            updates.connections.forEach((conn) => {
              const connId = generateId()
              connStmt.run(connId, id, conn.from, conn.to, now)
            })
          }
        }
      })()

      return true
    } catch (error) {
      console.error("Erro ao atualizar mapa mental:", error)
      return false
    }
  }

  delete(id: string): boolean {
    try {
      this.db.transaction(() => {
        // Deletar conexões
        const deleteConnsStmt = this.db.prepare("DELETE FROM conexoes WHERE mapa_id = ?")
        deleteConnsStmt.run(id)

        // Deletar nós
        const deleteNodesStmt = this.db.prepare("DELETE FROM nos WHERE mapa_id = ?")
        deleteNodesStmt.run(id)

        // Deletar mapa mental
        const deleteMapStmt = this.db.prepare("DELETE FROM mapas_mentais WHERE id = ?")
        deleteMapStmt.run(id)
      })()

      return true
    } catch (error) {
      console.error("Erro ao deletar mapa mental:", error)
      return false
    }
  }

  search(query: string): MindMap[] {
    try {
      const stmt = this.db.prepare(`
        SELECT DISTINCT m.id, m.titulo, m.descricao, m.created_at, m.updated_at
        FROM mapas_mentais m
        LEFT JOIN nos n ON m.id = n.mapa_id
        WHERE m.titulo LIKE ? OR m.descricao LIKE ? OR n.content LIKE ?
        ORDER BY m.updated_at DESC
      `)

      const searchTerm = `%${query}%`
      const maps = stmt.all(searchTerm, searchTerm, searchTerm) as any[]

      return maps.map((map) => ({
        id: map.id,
        titulo: map.titulo,
        descricao: map.descricao,
        nodes: [],
        connections: [],
        created_at: map.created_at,
        updated_at: map.updated_at,
      }))
    } catch (error) {
      console.error("Erro ao pesquisar mapas mentais:", error)
      return []
    }
  }
}

// Classe principal para operações de banco de dados
export class DatabaseOperations {
  public configRepo: ConfigRepository
  public mindMapRepo: MindMapRepository

  constructor() {
    this.configRepo = new ConfigRepositoryImpl()
    this.mindMapRepo = new MindMapRepositoryImpl()
  }

  // Operações de backup
  backup(): string {
    try {
      const db = getDatabase()
      const backup = db.serialize()
      return Buffer.from(backup).toString("base64")
    } catch (error) {
      console.error("Erro ao fazer backup:", error)
      throw error
    }
  }

  restore(backupData: string): boolean {
    try {
      const db = getDatabase()
      const data = Buffer.from(backupData, "base64")
      db.deserialize(data)
      return true
    } catch (error) {
      console.error("Erro ao restaurar backup:", error)
      return false
    }
  }

  // Estatísticas
  getStats() {
    try {
      const db = getDatabase()

      const mapsCount = db.prepare("SELECT COUNT(*) as count FROM mapas_mentais").get() as { count: number }
      const nodesCount = db.prepare("SELECT COUNT(*) as count FROM nos").get() as { count: number }
      const connectionsCount = db.prepare("SELECT COUNT(*) as count FROM conexoes").get() as { count: number }
      const configsCount = db.prepare("SELECT COUNT(*) as count FROM configuracoes").get() as { count: number }

      return {
        mapas_mentais: mapsCount.count,
        nos: nodesCount.count,
        conexoes: connectionsCount.count,
        configuracoes: configsCount.count,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      console.error("Erro ao obter estatísticas:", error)
      return {
        mapas_mentais: 0,
        nos: 0,
        conexoes: 0,
        configuracoes: 0,
        timestamp: new Date().toISOString(),
      }
    }
  }

  // Limpeza de dados
  clearAllMaps(): boolean {
    try {
      const db = getDatabase()

      db.transaction(() => {
        db.prepare("DELETE FROM conexoes").run()
        db.prepare("DELETE FROM nos").run()
        db.prepare("DELETE FROM mapas_mentais").run()
      })()

      return true
    } catch (error) {
      console.error("Erro ao limpar mapas:", error)
      return false
    }
  }

  // Verificação de saúde
  healthCheck(): { status: string; message: string; details: any } {
    try {
      const db = getDatabase()

      // Teste simples de conexão
      const result = db.prepare("SELECT 1 as test").get() as { test: number }

      if (result.test === 1) {
        const stats = this.getStats()
        return {
          status: "healthy",
          message: "Database is operational",
          details: stats,
        }
      } else {
        return {
          status: "error",
          message: "Database test failed",
          details: null,
        }
      }
    } catch (error) {
      return {
        status: "error",
        message: `Database error: ${error instanceof Error ? error.message : "Unknown error"}`,
        details: null,
      }
    }
  }
}

// Instância singleton
export const dbOps = new DatabaseOperations()

// Exportar repositórios individuais para compatibilidade
export const configRepo = dbOps.configRepo
export const mindMapRepo = dbOps.mindMapRepo

# 🚀 Deploy Manual do Mindscape

Como o arquivo `mindscape-deploy.tar.gz` já foi criado, siga estes passos para fazer o deploy manual:

## 📤 1. Upload do Arquivo

Use um cliente SCP/SFTP como WinSCP, FileZilla ou linha de comando:

### Usando WinSCP (Recomendado para Windows):
1. Baixe e instale o WinSCP: https://winscp.net/
2. Conecte com as credenciais:
   - **Host**: **************
   - **Usuário**: usuario
   - **Senha**: Senh@01020304
   - **Porta**: 22
3. Navegue até `/home/<USER>/`
4. Crie a pasta `mindscape-app` se não existir
5. Faça upload do arquivo `mindscape-deploy.tar.gz` para `/home/<USER>/mindscape-app/`

### Usando linha de comando (se tiver SSH client):
```bash
scp mindscape-deploy.tar.gz usuario@**************:/home/<USER>/mindscape-app/
```

## 🔧 2. Conectar ao Servidor

Use PuTTY ou outro cliente SSH:

### Usando PuTTY:
1. Baixe e instale o PuTTY: https://www.putty.org/
2. Conecte com:
   - **Host**: **************
   - **Usuário**: usuario
   - **Senha**: Senh@01020304

### Usando linha de comando:
```bash
ssh usuario@**************
```

## ⚙️ 3. Executar Comandos de Deploy

Após conectar ao servidor, execute os seguintes comandos:

```bash
# Ir para o diretório da aplicação
cd /home/<USER>/mindscape-app

# Atualizar sistema
sudo apt-get update -y

# Instalar Node.js 18+ se não estiver instalado
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar versão do Node.js
node --version

# Instalar PM2 para gerenciamento de processos
sudo npm install -g pm2

# Extrair aplicação
tar -xzf mindscape-deploy.tar.gz
rm mindscape-deploy.tar.gz

# Instalar dependências
npm install --production

# Fazer build da aplicação
npm run build

# Criar diretório de logs
mkdir -p logs

# Parar aplicação existente (se houver)
pm2 stop mindscape 2>/dev/null || true
pm2 delete mindscape 2>/dev/null || true

# Iniciar aplicação na porta 3030
pm2 start npm --name "mindscape" -- start

# Salvar configuração do PM2
pm2 save

# Configurar PM2 para iniciar automaticamente no boot
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u usuario --hp /home/<USER>

# Verificar status
pm2 status

# Ver logs
pm2 logs mindscape --lines 20
```

## 🌐 4. Verificar Aplicação

Após executar os comandos, verifique se a aplicação está funcionando:

```bash
# Verificar se está rodando na porta 3030
curl http://localhost:3030

# Ver status detalhado
pm2 show mindscape

# Monitorar em tempo real
pm2 monit
```

## 🔥 5. Configurar Firewall (se necessário)

```bash
# Permitir tráfego na porta 3030
sudo ufw allow 3030

# Verificar status do firewall
sudo ufw status
```

## 🎉 6. Acesso à Aplicação

Sua aplicação estará disponível em:

**http://**************:3030**

## 📊 Comandos Úteis para Monitoramento

```bash
# Ver logs em tempo real
pm2 logs mindscape

# Reiniciar aplicação
pm2 restart mindscape

# Ver uso de CPU e memória
pm2 monit

# Ver informações detalhadas
pm2 show mindscape

# Parar aplicação
pm2 stop mindscape

# Iniciar aplicação
pm2 start mindscape
```

## 🔧 Troubleshooting

### Se a aplicação não iniciar:
```bash
# Ver logs de erro
pm2 logs mindscape --err

# Verificar se a porta está livre
sudo lsof -i :3030

# Reiniciar PM2
pm2 kill
pm2 start npm --name "mindscape" -- start
```

### Se houver problemas de permissão:
```bash
# Corrigir permissões
sudo chown -R usuario:usuario /home/<USER>/mindscape-app
chmod -R 755 /home/<USER>/mindscape-app
```

### Se faltar memória:
```bash
# Verificar uso de memória
free -h

# Reiniciar aplicação
pm2 restart mindscape
```

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs: `pm2 logs mindscape`
2. Verifique o status: `pm2 status`
3. Teste conectividade: `curl http://localhost:3030`
4. Reinicie se necessário: `pm2 restart mindscape`

## ✅ Deploy Concluído!

Após seguir todos os passos, sua aplicação Mindscape estará rodando em:

**🌐 http://**************:3030**

Funcionalidades disponíveis:
- ✅ Criação de mapas mentais com IA
- ✅ Sistema de usuários e autenticação  
- ✅ Compartilhamento de mapas
- ✅ Proteção por senha
- ✅ Limitações para usuários não logados
- ✅ Sugestões inteligentes
- ✅ Interface responsiva
- ✅ Persistência de dados

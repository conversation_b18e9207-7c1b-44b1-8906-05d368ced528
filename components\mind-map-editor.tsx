"use client";

import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import useMindMapStore from "@/lib/store";
import { MindMap } from "@/types/mindmap";
import { MindMapCanvas } from "./mind-map-canvas";
import { ChatInterface } from "./chat-interface";
import { toast } from "@/hooks/use-toast";

interface MindMapEditorProps {
  mindMap: MindMap;
  onClose: () => void;
  isNewMap?: boolean;
}

export function MindMapEditor({ mindMap, onClose, isNewMap }: MindMapEditorProps) {
  const { setInitialState, setMindMapId, nodes, connections } = useMindMapStore();
  const [title, setTitle] = useState(mindMap.titulo);
  const titleInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (mindMap.dados) {
      const normalisedData = {
        nodes: mindMap.dados.nodes.map((n: any) => ({
          ...n,
          content: n.content ?? n.text ?? "",
        })),
        connections: mindMap.dados.connections.map((c: any) => ({
          from: c.from ?? c.source,
          to: c.to ?? c.target,
        })),
      };
      setInitialState(normalisedData);
      if (mindMap.id) setMindMapId(String(mindMap.id));
    }
  }, [mindMap, setInitialState, setMindMapId]);

  useEffect(() => {
    if (isNewMap && titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }
  }, [isNewMap]);

  async function handleSave() {
    const { mindMapId } = useMindMapStore.getState();
    try {
      const response = await fetch(`/api/mindmaps/${mindMapId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          titulo: title,
          dados: { nodes, connections },
        }),
      });

      if (!response.ok) {
        throw new Error("Falha ao salvar o mapa mental");
      }
      
      toast({
        title: "Sucesso!",
        description: "Mapa mental salvo com sucesso.",
      });

    } catch (e) {
      console.error("Erro ao salvar", e);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o mapa mental.",
        variant: "destructive",
      });
    }
  }

  function handleClose() {
    handleSave().then(() => {
        onClose();
    })
  }
  
  return (
    <div className="h-screen w-screen flex flex-col">
      <header className="p-4 border-b flex justify-between items-center gap-4">
        <Input
          ref={titleInputRef}
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="text-xl font-bold"
        />
        <div className="flex gap-2">
            <Button onClick={handleSave}>Salvar</Button>
            <Button onClick={handleClose} variant="outline">Voltar</Button>
        </div>
      </header>
      <ResizablePanelGroup direction="horizontal" className="w-full flex-grow">
        <ResizablePanel defaultSize={75}>
          <MindMapCanvas />
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel defaultSize={25}>
          <ChatInterface />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const { provider, config } = await request.json()

    if (provider === "openrouter") {
      const { apiKey } = config

      if (!apiKey) {
        return NextResponse.json({
          success: false,
          message: "❌ Chave da API é obrigatória",
        })
      }

      // Testar conexão com OpenRouter
      const response = await fetch("https://openrouter.ai/api/v1/models", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()
        return NextResponse.json({
          success: true,
          message: `✅ Conexão OpenRouter bem-sucedida! ${data.data?.length || 0} modelos disponíveis`,
        })
      } else {
        return NextResponse.json({
          success: false,
          message: `❌ Erro na conexão OpenRouter: ${response.status} ${response.statusText}`,
        })
      }
    } else if (provider === "ollama") {
      const { baseUrl } = config

      if (!baseUrl) {
        return NextResponse.json({
          success: false,
          message: "❌ URL do servidor é obrigatória",
        })
      }

      // Testar conexão com Ollama
      const cleanUrl = baseUrl.replace(/\/$/, "")
      const response = await fetch(`${cleanUrl}/api/tags`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      })

      if (response.ok) {
        const data = await response.json()
        return NextResponse.json({
          success: true,
          message: `✅ Conexão Ollama bem-sucedida! ${data.models?.length || 0} modelos disponíveis`,
        })
      } else {
        return NextResponse.json({
          success: false,
          message: `❌ Erro na conexão Ollama: ${response.status} ${response.statusText}`,
        })
      }
    } else {
      return NextResponse.json({
        success: false,
        message: "❌ Provedor não suportado",
      })
    }
  } catch (error) {
    console.error("❌ Erro ao testar conexão:", error)
    return NextResponse.json({
      success: false,
      message: `❌ Erro ao testar conexão: ${error}`,
    })
  }
}

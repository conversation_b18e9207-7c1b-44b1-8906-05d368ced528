# PowerShell Deploy Script for Mindscape
# Server: **************
# User: usuario
# Port: 3030

Write-Host "🚀 Starting Mindscape deployment..." -ForegroundColor Blue

# Server configuration
$SERVER_HOST = "**************"
$SERVER_USER = "usuario"
$SERVER_PASSWORD = "Senh@01020304"
$APP_DIR = "/home/<USER>/mindscape-app"
$APP_PORT = "3030"

Write-Host "📦 Preparing application for deployment..." -ForegroundColor Yellow

# Create deployment package (excluding node_modules and .git)
Write-Host "Creating deployment package..." -ForegroundColor Yellow

# Create a temporary directory for deployment files
$tempDir = "mindscape-deploy-temp"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Copy files excluding certain directories
$excludeDirs = @("node_modules", ".git", ".next", "logs", "mindscape-deploy-temp")
$excludeFiles = @("*.log", "deploy.ps1", "deploy.sh", "mindscape-deploy.tar.gz")

Get-ChildItem -Path "." | Where-Object { 
    $_.Name -notin $excludeDirs 
} | Copy-Item -Destination $tempDir -Recurse -Force

Write-Host "✅ Deployment package prepared" -ForegroundColor Green

# Create tar.gz using 7-Zip or built-in compression
if (Get-Command "7z" -ErrorAction SilentlyContinue) {
    7z a -ttar mindscape-deploy.tar $tempDir\*
    7z a -tgzip mindscape-deploy.tar.gz mindscape-deploy.tar
    Remove-Item mindscape-deploy.tar
} else {
    # Use PowerShell compression as fallback
    Compress-Archive -Path "$tempDir\*" -DestinationPath "mindscape-deploy.zip" -Force
    Write-Host "Created ZIP package (will extract on server)" -ForegroundColor Yellow
}

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force

Write-Host "🔄 Connecting to server and deploying..." -ForegroundColor Blue

# Check if plink (PuTTY) is available for SSH
if (-not (Get-Command "plink" -ErrorAction SilentlyContinue)) {
    Write-Host "❌ PuTTY plink not found. Please install PuTTY or use manual deployment." -ForegroundColor Red
    Write-Host "Manual deployment steps:" -ForegroundColor Yellow
    Write-Host "1. Upload the deployment package to the server" -ForegroundColor White
    Write-Host "2. SSH to $SERVER_USER@$SERVER_HOST" -ForegroundColor White
    Write-Host "3. Follow the manual steps in DEPLOY.md" -ForegroundColor White
    exit 1
}

# Deploy using plink (PuTTY)
Write-Host "📤 Uploading application files..." -ForegroundColor Yellow

# Upload file using pscp (PuTTY SCP)
if (Test-Path "mindscape-deploy.tar.gz") {
    & pscp -pw $SERVER_PASSWORD mindscape-deploy.tar.gz "$SERVER_USER@$SERVER_HOST`:$APP_DIR/"
} elseif (Test-Path "mindscape-deploy.zip") {
    & pscp -pw $SERVER_PASSWORD mindscape-deploy.zip "$SERVER_USER@$SERVER_HOST`:$APP_DIR/"
}

# Execute deployment commands on server
$deployCommands = @"
cd $APP_DIR

echo "🔧 Setting up server environment..."

# Update system
sudo apt-get update -y

# Install Node.js 18+ if not installed
if ! command -v node &> /dev/null || [[ `$(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 18 ]]; then
    echo "📦 Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2 for process management
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

echo "📦 Extracting application..."
if [ -f mindscape-deploy.tar.gz ]; then
    tar -xzf mindscape-deploy.tar.gz
    rm mindscape-deploy.tar.gz
elif [ -f mindscape-deploy.zip ]; then
    unzip -o mindscape-deploy.zip
    rm mindscape-deploy.zip
fi

echo "📦 Installing dependencies..."
npm install --production

echo "🔧 Building application..."
npm run build

echo "⚙️ Creating PM2 ecosystem file..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscape',
    script: 'npm',
    args: 'start',
    cwd: '$APP_DIR',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: '$APP_PORT'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# Create logs directory
mkdir -p logs

# Stop existing application if running
pm2 stop mindscape 2>/dev/null || true
pm2 delete mindscape 2>/dev/null || true

echo "🚀 Starting application on port $APP_PORT..."
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

echo "✅ Application deployed successfully!"
echo "🌐 Access your application at: http://$SERVER_HOST:$APP_PORT"

# Show application status
pm2 status
pm2 logs mindscape --lines 10
"@

# Execute commands on server
Write-Host "🔧 Executing deployment commands on server..." -ForegroundColor Yellow
echo $deployCommands | & plink -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_HOST" -batch

# Cleanup local files
if (Test-Path "mindscape-deploy.tar.gz") {
    Remove-Item "mindscape-deploy.tar.gz"
}
if (Test-Path "mindscape-deploy.zip") {
    Remove-Item "mindscape-deploy.zip"
}

Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "📱 Your Mindscape application is now running at:" -ForegroundColor Blue
Write-Host "   http://$SERVER_HOST`:$APP_PORT" -ForegroundColor Green
Write-Host "📊 To monitor the application:" -ForegroundColor Yellow
Write-Host "   ssh $SERVER_USER@$SERVER_HOST" -ForegroundColor White
Write-Host "   pm2 status" -ForegroundColor White
Write-Host "   pm2 logs mindscape" -ForegroundColor White

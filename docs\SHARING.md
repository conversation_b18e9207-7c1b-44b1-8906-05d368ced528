# 🤝 Sistema de Compartilhamento - Mindscape

O Mindscape agora inclui um sistema completo de compartilhamento de mapas mentais que permite colaboração entre usuários.

## 🚀 Funcionalidades

### ✅ **Compartilhamento por Email**
- **Compartilhar por email** com usuários cadastrados na plataforma
- **Permissões granulares**: Visualizar apenas ou Visualizar + Editar
- **Validação de usuário**: Só permite compartilhar com emails existentes
- **Prevenção de auto-compartilhamento**: Não permite compartilhar consigo mesmo

### ✅ **Interface Dual**
- **"Meus Mapas"**: Mapas criados pelo usuário
- **"Mapas Compartilhados"**: Mapas compartilhados com o usuário
- **Navegação por abas** intuitiva
- **Contadores dinâmicos** de mapas em cada seção

### ✅ **Controle de Permissões**
- **Visualizar**: Usuário pode apenas ver o mapa mental
- **Editar**: Usuário pode ver e modificar o mapa mental
- **Indicadores visuais** de permissão nos cards
- **Botões contextuais** baseados na permissão

### ✅ **Gerenciamento de Compartilhamentos**
- **Lista de compartilhamentos** para cada mapa
- **Remover compartilhamentos** facilmente
- **Informações do proprietário** nos mapas compartilhados
- **Atualização em tempo real** da lista de compartilhamentos

## 🔧 Como Usar

### **1. Compartilhar um Mapa Mental**
1. Vá para a aba "Meus Mapas"
2. Clique no ícone de compartilhar (🔗) no mapa desejado
3. Digite o email do usuário destinatário
4. Escolha a permissão (Ver ou Editar)
5. Clique em "Compartilhar"

### **2. Visualizar Mapas Compartilhados**
1. Clique na aba "Mapas Compartilhados"
2. Veja todos os mapas compartilhados com você
3. Informações do proprietário são exibidas
4. Permissões são indicadas por badges

### **3. Gerenciar Compartilhamentos**
1. Abra o modal de compartilhamento de um mapa seu
2. Veja a lista "Compartilhado com:"
3. Clique no ícone de lixeira para remover um compartilhamento

### **4. Editar Mapas Compartilhados**
1. Na aba "Mapas Compartilhados"
2. Clique em "Editar" (se tiver permissão)
3. Ou "Ver" (se tiver apenas permissão de visualização)

## 🛠️ Arquitetura Técnica

### **APIs de Compartilhamento**
```
POST /api/mindmaps/[id]/share     - Compartilhar mapa mental
GET  /api/mindmaps/[id]/share     - Listar compartilhamentos
DELETE /api/mindmaps/[id]/share   - Remover compartilhamento
GET  /api/shared-mindmaps         - Mapas compartilhados comigo
```

### **Banco de Dados**
- **Arquivo**: `data/shared_mindmaps.json`
- **Estrutura**:
```json
{
  "id": "share_uuid",
  "mindmap_id": "mapa_uuid",
  "owner_id": "proprietario_uuid", 
  "shared_with_id": "destinatario_uuid",
  "shared_with_email": "<EMAIL>",
  "permission": "view|edit",
  "created_at": "2025-01-01T00:00:00.000Z",
  "updated_at": "2025-01-01T00:00:00.000Z"
}
```

### **Componentes React**
- `ShareModal` - Modal de compartilhamento com formulário
- Interface dual em `MainApp` - Abas Meus/Compartilhados
- Botões contextuais baseados em permissões
- Badges de permissão e proprietário

## 🔒 Segurança e Validação

### **Validação de Entrada**
```typescript
const shareSchema = z.object({
  email: z.string().email('Email inválido'),
  permission: z.enum(['view', 'edit']).default('view')
})
```

### **Verificações de Segurança**
- **Propriedade**: Só o dono pode compartilhar
- **Existência do usuário**: Email deve estar cadastrado
- **Auto-compartilhamento**: Bloqueado
- **Duplicação**: Não permite compartilhar duas vezes com o mesmo usuário

### **Controle de Acesso**
```typescript
// Verificar se o usuário é dono do mapa
const mindMap = await getMindMapById(mindmapId)
if (!mindMap || mindMap.user_id !== user.userId) {
  return NextResponse.json({ error: 'Sem permissão' }, { status: 404 })
}
```

## 🎯 Fluxos de Uso

### **Fluxo de Compartilhamento**
1. **Usuário A** cria um mapa mental
2. **Usuário A** clica em compartilhar
3. **Usuário A** digita email do **Usuário B**
4. **Sistema** valida se **Usuário B** existe
5. **Sistema** cria registro de compartilhamento
6. **Usuário B** vê o mapa na aba "Compartilhados"

### **Fluxo de Colaboração**
1. **Usuário B** acessa "Mapas Compartilhados"
2. **Usuário B** vê mapa compartilhado por **Usuário A**
3. **Usuário B** clica em "Editar" (se tiver permissão)
4. **Usuário B** faz alterações no mapa
5. **Alterações** são salvas e visíveis para **Usuário A**

### **Fluxo de Remoção**
1. **Usuário A** abre modal de compartilhamento
2. **Usuário A** vê lista de compartilhamentos
3. **Usuário A** clica em remover compartilhamento
4. **Usuário B** perde acesso ao mapa

## 📊 Interface do Usuário

### **Abas de Navegação**
```tsx
<Button variant={currentView === 'my-maps' ? 'default' : 'outline'}>
  <FileText className="h-4 w-4 mr-2" />
  Meus Mapas
</Button>
<Button variant={currentView === 'shared-maps' ? 'default' : 'outline'}>
  <Users className="h-4 w-4 mr-2" />
  Compartilhados
</Button>
```

### **Cards de Mapas Compartilhados**
- **Título do mapa**
- **"Compartilhado por [Nome]"**
- **Badge de permissão** (Ver/Editar)
- **Botão contextual** (Ver/Editar)
- **Sem botão de exclusão** (não é dono)

### **Modal de Compartilhamento**
- **Formulário de email** com validação
- **Seletor de permissão** (Ver/Editar)
- **Lista de compartilhamentos** atuais
- **Botões de remoção** para cada compartilhamento

## 🔄 Compatibilidade

### **Backward Compatibility**
- ✅ **Mapas existentes** continuam funcionando
- ✅ **Usuários não logados** podem usar normalmente
- ✅ **Funcionalidades antigas** inalteradas
- ✅ **Migração suave** sem perda de dados

### **Integração com Autenticação**
- **Requer login** para compartilhar/ver compartilhados
- **Funciona sem login** para mapas próprios
- **Atualização automática** após login/logout

## 🚀 Deploy e Produção

### **Arquivos de Dados**
```
data/
├── mindmaps.json          # Mapas mentais
├── users.json             # Usuários
└── shared_mindmaps.json   # Compartilhamentos (NOVO)
```

### **Backup Recomendado**
- **Backup regular** de `shared_mindmaps.json`
- **Sincronização** entre arquivos de dados
- **Validação de integridade** dos relacionamentos

## 🧪 Testando o Sistema

### **Teste de Compartilhamento**
```bash
# 1. Compartilhar mapa
curl -X POST http://localhost:3000/api/mindmaps/MAP_ID/share \
  -H "Content-Type: application/json" \
  -H "Cookie: auth-token=TOKEN" \
  -d '{"email":"<EMAIL>","permission":"edit"}'

# 2. Listar compartilhamentos
curl http://localhost:3000/api/mindmaps/MAP_ID/share \
  -H "Cookie: auth-token=TOKEN"

# 3. Ver mapas compartilhados
curl http://localhost:3000/api/shared-mindmaps \
  -H "Cookie: auth-token=TOKEN"
```

### **Cenários de Teste**
1. **Compartilhar com usuário existente** ✅
2. **Tentar compartilhar com email inexistente** ❌
3. **Tentar compartilhar consigo mesmo** ❌
4. **Compartilhar duas vezes com mesmo usuário** ❌
5. **Editar mapa compartilhado com permissão** ✅
6. **Tentar editar mapa só com permissão de visualização** ❌
7. **Remover compartilhamento** ✅

---

**🎉 O sistema de compartilhamento está totalmente funcional e pronto para colaboração!**

import bcrypt from "bcryptjs"
import crypto from "crypto"

const SALT_ROUNDS = 12
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || "default-key-change-in-production"

export async function hashPassword(password: string): Promise<string> {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS)
    const hash = await bcrypt.hash(password, salt)
    return hash
  } catch (error) {
    console.error("Error hashing password:", error)
    throw new Error("Failed to hash password")
  }
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash)
  } catch (error) {
    console.error("Error verifying password:", error)
    return false
  }
}

export function encryptData(data: string): string {
  try {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher("aes-256-cbc", ENCRYPTION_KEY)
    let encrypted = cipher.update(data, "utf8", "hex")
    encrypted += cipher.final("hex")
    return iv.toString("hex") + ":" + encrypted
  } catch (error) {
    console.error("Error encrypting data:", error)
    throw new Error("Failed to encrypt data")
  }
}

export function decryptData(encryptedData: string): string {
  try {
    const parts = encryptedData.split(":")
    const iv = Buffer.from(parts[0], "hex")
    const encrypted = parts[1]
    const decipher = crypto.createDecipher("aes-256-cbc", ENCRYPTION_KEY)
    let decrypted = decipher.update(encrypted, "hex", "utf8")
    decrypted += decipher.final("utf8")
    return decrypted
  } catch (error) {
    console.error("Error decrypting data:", error)
    throw new Error("Failed to decrypt data")
  }
}

export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, "") // Remove potential HTML tags
    .replace(/['"]/g, "") // Remove quotes
    .trim()
}

export function generateSecureToken(): string {
  return crypto.randomBytes(32).toString("hex")
}

export class SecurityManager {
  static async hashPassword(password: string): Promise<string> {
    return hashPassword(password)
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return verifyPassword(password, hash)
  }

  static encryptData(data: string): string {
    return encryptData(data)
  }

  static decryptData(encryptedData: string): string {
    return decryptData(encryptedData)
  }

  static sanitizeInput(input: string): string {
    return sanitizeInput(input)
  }

  static generateSecureToken(): string {
    return generateSecureToken()
  }

  static validatePassword(password: string): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long")
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter")
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter")
    }

    if (!/[0-9]/.test(password)) {
      errors.push("Password must contain at least one number")
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  static rateLimit = new Map<string, { count: number; resetTime: number }>()

  static checkRateLimit(identifier: string, maxAttempts = 5, windowMs: number = 15 * 60 * 1000): boolean {
    const now = Date.now()
    const record = this.rateLimit.get(identifier)

    if (!record || now > record.resetTime) {
      this.rateLimit.set(identifier, { count: 1, resetTime: now + windowMs })
      return true
    }

    if (record.count >= maxAttempts) {
      return false
    }

    record.count++
    return true
  }
}

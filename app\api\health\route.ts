import { NextResponse } from "next/server"
import { getDatabaseStats } from "@/lib/database"

export async function GET() {
  try {
    const stats = await getDatabaseStats()
    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      database: {
        connected: true,
        stats,
      },
    })
  } catch (error) {
    console.error("Health check failed:", error)
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: `Health check failed: ${error}`,
      },
      { status: 500 },
    )
  }
}

// Modelos de dados para persistência

import type { Node, Connection } from "@/types/mindmap"

export interface MindMapNode {
  id: string
  content: string
  x: number
  y: number
  color: string
  size: "small" | "medium" | "large"
  metadata?: {
    createdAt?: string
    updatedAt?: string
    tags?: string[]
    priority?: number
  }
}

export interface MindMapConnection {
  id: string
  fromId: string
  toId: string
  type: "semantic" | "manual"
  strength: number
  metadata?: {
    createdAt?: string
    label?: string
    style?: "solid" | "dashed" | "dotted"
  }
}

export interface PersistedMindMap {
  id: string
  name: string
  description: string
  nodes: Node[]
  connections: Connection[]
  protectedByPassword: boolean
  createdAt: string
  updatedAt: string
  version: number
  tags: string[]
  metadata: {
    nodeCount: number
    connectionCount: number
    createdBy: string
    lastModifiedBy?: string
    version: string
  }
}

export interface UserPreferences {
  theme: "claro" | "escuro"
  defaultZoom: number
  showGrid: boolean
  autoSave: boolean
  language: string
  notifications: {
    enabled: boolean
    types: string[]
  }
}

export interface SystemConfiguration {
  llm_provider: "openrouter" | "ollama"
  openrouter_api_key: string
  ollama_base_url: string
  modelo_padrao: string
  tema: "claro" | "escuro"
  auto_salvar: boolean
  zoom_padrao: number
  grade_visivel: boolean
}

// Validadores de dados
export class DataValidator {
  static validateNode(node: any): node is Node {
    return (
      node &&
      typeof node.id === "string" &&
      typeof node.content === "string" &&
      typeof node.x === "number" &&
      typeof node.y === "number" &&
      typeof node.color === "string" &&
      ["small", "medium", "large"].includes(node.size)
    )
  }

  static validateConnection(connection: any): connection is Connection {
    return (
      connection &&
      typeof connection.id === "string" &&
      typeof connection.fromId === "string" &&
      typeof connection.toId === "string" &&
      ["semantic", "manual"].includes(connection.type) &&
      typeof connection.strength === "number" &&
      connection.strength >= 0 &&
      connection.strength <= 1
    )
  }

  static validatePersistedMindMapData(data: any): data is PersistedMindMap {
    return (
      data &&
      typeof data.id === "string" &&
      typeof data.name === "string" &&
      Array.isArray(data.nodes) &&
      Array.isArray(data.connections) &&
      data.nodes.every(this.validateNode) &&
      data.connections.every(this.validateConnection)
    )
  }
}

// Utilitários de transformação
export class DataTransformer {
  static toPersistedFormat(mindMapData: any): Omit<PersistedMindMap, "createdAt" | "updatedAt"> {
    return {
      id: mindMapData.id,
      name: mindMapData.name,
      description: mindMapData.description || "",
      nodes: mindMapData.nodes,
      connections: mindMapData.connections,
      protectedByPassword: mindMapData.protectedByPassword || false,
      version: 1,
      tags: [],
      metadata: {
        nodeCount: mindMapData.nodes.length,
        connectionCount: mindMapData.connections.length,
        createdBy: "user",
        version: "1.0.0",
      },
    }
  }

  static fromPersistedFormat(persistedData: PersistedMindMap): any {
    return {
      id: persistedData.id,
      name: persistedData.name,
      description: persistedData.description,
      nodes: persistedData.nodes,
      connections: persistedData.connections,
      protectedByPassword: persistedData.protectedByPassword,
      createdAt: persistedData.createdAt,
      updatedAt: persistedData.updatedAt,
    }
  }
}

import { NextResponse } from "next/server"
import { getDatabase } from "@/lib/database"

// Modelos OpenRouter populares
const OPENROUTER_MODELS = [
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "openrouter",
    description: "Modelo mais avançado da OpenAI",
    contextLength: 128000,
    pricing: { input: 0.005, output: 0.015 },
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "openrouter",
    description: "Versão mais rápida e econômica do GPT-4o",
    contextLength: 128000,
    pricing: { input: 0.00015, output: 0.0006 },
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "openrouter",
    description: "Excelente para análise e raciocínio",
    contextLength: 200000,
    pricing: { input: 0.003, output: 0.015 },
  },
  {
    id: "google/gemini-pro-1.5",
    name: "Gemini Pro 1.5",
    provider: "openrouter",
    description: "Modelo avançado do Google",
    contextLength: 1000000,
    pricing: { input: 0.00125, output: 0.005 },
  },
  {
    id: "meta-llama/llama-3.1-70b-instruct",
    name: "Llama 3.1 70B",
    provider: "openrouter",
    description: "Modelo open source da Meta",
    contextLength: 131072,
    pricing: { input: 0.00088, output: 0.00088 },
  },
  {
    id: "mistralai/mistral-7b-instruct",
    name: "Mistral 7B",
    provider: "openrouter",
    description: "Modelo eficiente e rápido",
    contextLength: 32768,
    pricing: { input: 0.00013, output: 0.00013 },
  },
]

// Função para obter modelos do Ollama
async function getOllamaModels(baseUrl: string) {
  try {
    const cleanUrl = baseUrl.replace(/\/$/, "")
    const response = await fetch(`${cleanUrl}/api/tags`, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    })

    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`)
    }

    const data = await response.json()
    return (
      data.models?.map((model: any) => ({
        id: model.name,
        name: model.name.split(":")[0].toUpperCase(),
        provider: "ollama",
        description: `Modelo local - ${model.name}`,
        contextLength: 4096, // Valor padrão
        size: model.size,
        modified: model.modified_at,
      })) || []
    )
  } catch (error) {
    console.error("❌ Erro ao buscar modelos Ollama:", error)
    return []
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const provider = searchParams.get("provider")

    // Obter configurações do banco
    const db = getDatabase()
    const configs = db.prepare("SELECT chave, valor FROM configuracoes").all()
    const configObj: any = {}
    configs.forEach((config: any) => {
      configObj[config.chave] = config.valor
    })

    const llmProvider = provider || configObj.llm_provider || "openrouter"
    let models: any[] = []

    if (llmProvider === "openrouter") {
      models = OPENROUTER_MODELS
    } else if (llmProvider === "ollama") {
      const ollamaUrl = configObj.ollama_base_url || "http://localhost:11434"
      models = await getOllamaModels(ollamaUrl)
    } else {
      // Retornar ambos se não especificado
      const ollamaUrl = configObj.ollama_base_url || "http://localhost:11434"
      const ollamaModels = await getOllamaModels(ollamaUrl)
      models = [...OPENROUTER_MODELS, ...ollamaModels]
    }

    return NextResponse.json(models)
  } catch (error) {
    console.error("❌ Erro ao carregar modelos:", error)
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: "Não foi possível carregar os modelos disponíveis",
        models: OPENROUTER_MODELS, // Fallback para modelos OpenRouter
      },
      { status: 500 },
    )
  }
}

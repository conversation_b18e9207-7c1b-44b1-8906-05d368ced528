# 🎯 Sistema de Usuários Não Logados - Mindscape

O Mindscape agora inclui um sistema de limitação para usuários não logados que permite experimentar a geração de mapas mentais com limitações, incentivando o cadastro para funcionalidades completas.

## 🚀 Funcionalidades

### ✅ **Modo Demonstração para Usuários Não Logados**
- **Limitação de 8 nós**: Máximo de 7 nós principais + 1 nó central
- **Visualização temporária**: Mapas gerados não são salvos permanentemente
- **Experiência completa**: Podem visualizar e interagir com o mapa gerado
- **Convite para cadastro**: Modal atrativo após gerar o primeiro mapa

### ✅ **Sistema de Conversão**
- **Banner informativo**: Explica limitações e benefícios do cadastro
- **Modal de convite**: Aparece após gerar mapa com incentivos visuais
- **Botões adaptativos**: "Experimentar Grátis" vs "Gerar Mapa Mental"
- **Fluxo suave**: Transição natural para registro/login

### ✅ **Experiência Premium para Usuários Logados**
- **Mapas ilimitados**: Sem limitação de número de nós
- **Salvamento permanente**: Todos os mapas são salvos automaticamente
- **Funcionalidades completas**: Acesso a todas as features
- **Compartilhamento**: Pode compartilhar mapas com outros usuários

## 🔧 Como Funciona

### **1. Detecção de Usuário Não Logado**
- **Verificação automática**: Sistema detecta se usuário está logado
- **Interface adaptativa**: Elementos visuais se ajustam ao status
- **Limitações aplicadas**: Restrições ativadas automaticamente

### **2. Geração Limitada de Mapas**
- **API modificada**: Parâmetro `isGuest` enviado para backend
- **Prompt ajustado**: IA instruída a gerar máximo 7 nós principais
- **Processamento limitado**: Algoritmo corta nós extras se necessário

### **3. Experiência de Conversão**
- **Banner informativo**: Mostra benefícios do cadastro
- **Mapa temporário**: Permite interação completa sem salvar
- **Modal de convite**: Aparece 2 segundos após geração
- **Call-to-action**: Botões claros para registro

## 🛠️ Arquitetura Técnica

### **Frontend - Detecção de Status**
```typescript
// Verificação de usuário logado
const { user } = useAuth()

// Interface adaptativa
{!user && (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <span className="font-medium">Modo Demonstração</span>
    <p>Experimente gratuitamente! Gere um mapa mental com até 8 nós.</p>
  </div>
)}
```

### **API - Limitação de Nós**
```typescript
// app/api/chat/route.ts
const { isGuest = false } = body

// Limitação aplicada
const maxNodes = isGuest ? 7 : lines.length
const nodesToProcess = Math.min(lines.length, maxNodes)

// Prompt ajustado
const nodeLimit = isGuest ? "máximo 7 nós principais" : "detalhado"
```

### **Geração Temporária**
```typescript
// Para usuários não logados
if (!user) {
  const tempMindMap = {
    id: 'temp-preview',
    titulo: data.mindMap.name || prompt,
    dados: data.mindMap,
    // ... dados temporários
  }
  setSelectedMindMap(tempMindMap)
  // Não salva no banco de dados
}
```

## 🎯 Fluxos de Uso

### **Fluxo de Usuário Não Logado**
1. **Acessa aplicação** sem fazer login
2. **Vê banner informativo** sobre modo demonstração
3. **Digita tópico** e clica "Experimentar Grátis"
4. **Sistema gera mapa** limitado a 8 nós
5. **Visualiza mapa temporário** com interação completa
6. **Recebe toast** informativo sobre limitações
7. **Modal de convite** aparece após 2 segundos
8. **Pode continuar explorando** ou criar conta

### **Fluxo de Conversão**
1. **Usuário gera mapa** no modo demonstração
2. **Modal atrativo** mostra benefícios do cadastro
3. **Opções claras**: "Continuar Explorando" ou "Criar Conta Grátis"
4. **Se escolher cadastro**: Modal de auth abre automaticamente
5. **Após registro**: Acesso completo às funcionalidades

### **Fluxo de Usuário Logado**
1. **Faz login** na aplicação
2. **Interface completa** sem limitações
3. **Gera mapas ilimitados** com todos os nós necessários
4. **Salva automaticamente** todos os mapas
5. **Acesso a funcionalidades premium** (compartilhamento, proteção, etc.)

## 📊 Interface do Usuário

### **Banner Informativo (Usuários Não Logados)**
```tsx
<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
  <div className="flex items-center justify-center gap-2 text-blue-800">
    <Brain className="h-5 w-5" />
    <span className="font-medium">Modo Demonstração</span>
  </div>
  <p className="text-sm text-blue-700 mt-2 text-center">
    Experimente gratuitamente! Gere um mapa mental com até 8 nós. 
    <br />
    <strong>Faça login ou cadastre-se</strong> para criar mapas ilimitados e salvá-los.
  </p>
</div>
```

### **Modal de Convite para Cadastro**
- **Título atrativo**: "Gostou do seu mapa mental?"
- **Parabenização**: "🎉 Parabéns! Você criou seu primeiro mapa mental"
- **Benefícios visuais**: Ícones e descrições dos recursos premium
- **Call-to-action**: Botão gradiente "Criar Conta Grátis"

### **Botões Adaptativos**
```tsx
// Texto do botão baseado no status do usuário
{!user ? "Experimentar Grátis" : t.mainApp.generate}
```

## 🔒 Limitações Implementadas

### **Para Usuários Não Logados**
- **Máximo 8 nós**: 1 central + 7 principais
- **Sem salvamento**: Mapas são temporários
- **Sem compartilhamento**: Não podem compartilhar mapas
- **Sem proteção por senha**: Funcionalidade premium
- **Sem histórico**: Não mantém mapas anteriores

### **Prompts de IA Ajustados**
```typescript
// Prompt específico para guests
const systemPrompt = `... ${isGuest ? "IMPORTANTE: Crie no máximo 7 nós principais além do nó central." : ""} ...`
const userPrompt = `Crie um mapa mental ${nodeLimit} sobre: ${message}.`
```

### **Algoritmo de Limitação**
```typescript
// Corte automático se IA gerar mais nós
const maxNodes = isGuest ? 7 : lines.length
const nodesToProcess = Math.min(lines.length, maxNodes)
```

## 🎯 Estratégia de Conversão

### **Incentivos Visuais**
1. **💾 Salvar seus mapas**: "Acesse seus mapas a qualquer momento"
2. **🧠 Mapas ilimitados**: "Crie quantos mapas quiser, sem limitações"
3. **👥 Compartilhar mapas**: "Colabore com outros usuários"

### **Timing Otimizado**
- **Banner imediato**: Visível desde o primeiro acesso
- **Toast após geração**: Feedback positivo + convite
- **Modal com delay**: 2 segundos após geração para não interromper

### **Mensagens Persuasivas**
- **"Modo Demonstração"**: Deixa claro que é limitado
- **"Experimentar Grátis"**: Convida à ação sem compromisso
- **"Este é apenas o começo!"**: Sugere potencial maior

## 🧪 Testando o Sistema

### **Teste como Usuário Não Logado**
1. **Acesse** http://localhost:3000 sem fazer login
2. **Observe** o banner azul "Modo Demonstração"
3. **Digite** um tópico qualquer
4. **Clique** "Experimentar Grátis"
5. **Verifique** que o mapa tem máximo 8 nós
6. **Aguarde** o modal de convite aparecer
7. **Teste** os botões do modal

### **Teste de Conversão**
1. **Gere um mapa** como usuário não logado
2. **Aguarde** o modal de convite
3. **Clique** "Criar Conta Grátis"
4. **Verifique** que o modal de auth abre
5. **Complete** o registro
6. **Confirme** acesso às funcionalidades completas

### **Teste de Limitação**
1. **Use tópico complexo** que geraria muitos nós
2. **Verifique** que máximo 8 nós são criados
3. **Compare** com usuário logado (sem limitação)
4. **Confirme** que mapa não é salvo permanentemente

## 🔄 Compatibilidade

### **Backward Compatibility**
- ✅ **Usuários existentes** não afetados
- ✅ **Funcionalidades antigas** inalteradas
- ✅ **APIs existentes** mantidas
- ✅ **Dados salvos** preservados

### **Integração com Outros Sistemas**
- **Autenticação**: Funciona com sistema de login existente
- **Compartilhamento**: Disponível apenas para usuários logados
- **Proteção por senha**: Funcionalidade premium
- **Sistema de admin**: Não afetado pelas limitações

## 🚀 Deploy e Produção

### **Configurações Recomendadas**
- **Analytics**: Rastrear conversões de guest para usuário
- **A/B Testing**: Testar diferentes textos de convite
- **Métricas**: Monitorar taxa de conversão
- **Feedback**: Coletar opinões sobre experiência guest

### **Monitoramento**
- **Taxa de conversão**: % de guests que se cadastram
- **Tempo até conversão**: Quanto tempo levam para se cadastrar
- **Mapas gerados**: Quantos mapas guests criam antes de converter
- **Abandono**: Em que ponto guests saem sem converter

## 🎯 Benefícios do Sistema

### **Para o Negócio**
- **Aquisição de usuários**: Permite experimentar sem barreiras
- **Conversão qualificada**: Usuários já experimentaram o valor
- **Redução de fricção**: Não exige cadastro imediato
- **Demonstração de valor**: Mostra benefícios na prática

### **Para Usuários**
- **Experimentação livre**: Podem testar sem compromisso
- **Compreensão do valor**: Veem o que podem ganhar
- **Decisão informada**: Cadastram-se sabendo o que esperar
- **Experiência gradual**: Transição suave para usuário premium

### **Para o Produto**
- **Feedback early**: Usuários testam antes de se comprometer
- **Otimização de conversão**: Dados para melhorar funil
- **Segmentação clara**: Guests vs usuários registrados
- **Escalabilidade**: Sistema suporta muitos usuários experimentando

---

**🎯 O sistema de usuários não logados está totalmente funcional e otimizado para conversão!**

### **📈 Métricas de Sucesso**
- **Taxa de conversão**: % de guests que se cadastram
- **Engajamento**: Tempo gasto explorando mapas
- **Qualidade**: Satisfação dos usuários convertidos
- **Retenção**: % de usuários convertidos que continuam ativos

import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from './auth'

export function withAuth(handler: (request: NextRequest, context: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context: any) => {
    try {
      // Get token from cookie
      const token = request.cookies.get('auth-token')?.value
      
      if (!token) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Token de autenticação não encontrado' 
          },
          { status: 401 }
        )
      }
      
      // Verify token
      const payload = verifyToken(token)
      if (!payload) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Token de autenticação inválido' 
          },
          { status: 401 }
        )
      }
      
      // Add user info to request context
      const requestWithUser = request as NextRequest & { user: typeof payload }
      requestWithUser.user = payload
      
      return handler(requestWithUser, context)
      
    } catch (error) {
      console.error('Auth middleware error:', error)
      return NextResponse.json(
        { 
          success: false, 
          error: 'Erro de autenticação' 
        },
        { status: 500 }
      )
    }
  }
}

export function getUserFromRequest(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value
  if (!token) return null

  return verifyToken(token)
}

export function requireAdmin(request: NextRequest): boolean {
  const user = getUserFromRequest(request)
  return user?.isAdmin === true
}

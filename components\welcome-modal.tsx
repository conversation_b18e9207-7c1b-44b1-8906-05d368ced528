"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle, Brain, Zap, Save } from "lucide-react"

interface WelcomeModalProps {
  isOpen: boolean
  onClose: () => void
}

export function WelcomeModal({ isOpen, onClose }: WelcomeModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl text-center mb-4">Bem-vindo ao Mapa Mental IA! 🧠</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <p className="text-gray-600 text-center">
            Organize suas ideias visualmente com a ajuda da inteligência artificial
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <MessageCircle className="w-5 h-5 text-blue-500" />
                <h3 className="font-semibold">Modo Pergunta</h3>
              </div>
              <p className="text-sm text-gray-600">
                Faça perguntas e decida se quer adicionar as respostas como nós no seu mapa mental
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Brain className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold">Criação Completa</h3>
              </div>
              <p className="text-sm text-gray-600">Peça para criar um mapa mental completo sobre qualquer tópico</p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-5 h-5 text-yellow-500" />
                <h3 className="font-semibold">Conexões Inteligentes</h3>
              </div>
              <p className="text-sm text-gray-600">
                A IA identifica relacionamentos semânticos e conecta ideias automaticamente
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Save className="w-5 h-5 text-purple-500" />
                <h3 className="font-semibold">Salvar & Carregar</h3>
              </div>
              <p className="text-sm text-gray-600">Salve seus mapas mentais e acesse-os quando quiser</p>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Exemplos de uso:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• "Crie um mapa mental sobre inteligência artificial"</li>
              <li>• "O que é sustentabilidade?" (modo pergunta)</li>
              <li>• "Mapa mental para planejamento de projeto"</li>
              <li>• "Explique os conceitos de marketing digital"</li>
            </ul>
          </div>

          <div className="flex justify-center">
            <Button onClick={onClose} className="px-8">
              Começar a Usar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

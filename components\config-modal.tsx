"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Check, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useTranslation } from "@/lib/i18n"

interface ConfigModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ConfigModal({ isOpen, onClose }: ConfigModalProps) {
  const [config, setConfig] = useState({
    language: "pt",
    database_type: "neon",
    database_url: "",
    llm_provider: "openrouter",
    openrouter_api_key: "",
    ollama_base_url: "http://localhost:11434",
    default_model: "openai/gpt-4o-mini",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResults, setTestResults] = useState({
    database: null as boolean | null,
    ai: null as boolean | null,
  })
  const { toast } = useToast()
  const { t } = useTranslation()

  useEffect(() => {
    if (isOpen) {
      loadConfig()
    }
  }, [isOpen])

  const loadConfig = async () => {
    try {
      const response = await fetch("/api/config")
      if (response.ok) {
        const data = await response.json()
        setConfig({
          language: "pt", // Force Portuguese
          database_type: data.database_type || "neon",
          database_url: data.database_url || "",
          llm_provider: data.llm_provider || "openrouter",
          openrouter_api_key: data.openrouter_api_key || "",
          ollama_base_url: data.ollama_base_url || "http://localhost:11434",
          default_model: data.default_model || "openai/gpt-4o-mini",
        })
      }
    } catch (error) {
      console.error("Erro ao carregar configuração:", error)
    }
  }

  const testConnection = async (type: "database" | "ai") => {
    setIsTesting(true)
    try {
      const response = await fetch("/api/test-connection", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type,
          ...config,
        }),
      })

      const result = await response.json()
      setTestResults((prev) => ({ ...prev, [type]: result.success }))

      if (result.success) {
        toast({
          title: "Sucesso",
          description: t.config.connectionSuccess,
        })
      } else {
        toast({
          title: "Erro",
          description: `${t.config.connectionError}: ${result.error}`,
          variant: "destructive",
        })
      }
    } catch (error) {
      setTestResults((prev) => ({ ...prev, [type]: false }))
      toast({
        title: "Erro",
        description: `${t.config.connectionError}: ${error}`,
        variant: "destructive",
      })
    } finally {
      setIsTesting(false)
    }
  }

  const saveConfig = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/config", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...config,
          language: "pt", // Force Portuguese
        }),
      })

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: t.config.saveSuccess,
        })
        onClose()
      } else {
        throw new Error("Falha ao salvar configuração")
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: t.config.saveError,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t.config.title}</DialogTitle>
          <DialogDescription>Configure suas preferências e conexões da aplicação</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Language Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t.config.language}</CardTitle>
              <CardDescription>Idioma da interface (fixo em Português)</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  🇧🇷 Português (Brasil)
                </Badge>
                <Check className="h-4 w-4 text-green-500" />
              </div>
            </CardContent>
          </Card>

          {/* Database Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t.config.database}</CardTitle>
              <CardDescription>Configurações do banco de dados</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Tipo de Banco</Label>
                <Select
                  value={config.database_type}
                  onValueChange={(value) => setConfig((prev) => ({ ...prev, database_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sqlite">SQLite Local</SelectItem>
                    <SelectItem value="neon">Neon Database</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.database_type === "neon" && (
                <div>
                  <Label>URL do Banco de Dados</Label>
                  <Input
                    type="password"
                    placeholder="postgresql://user:password@host:port/database"
                    value={config.database_url}
                    onChange={(e) => setConfig((prev) => ({ ...prev, database_url: e.target.value }))}
                  />
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => testConnection("database")}
                  disabled={isTesting || (config.database_type === "neon" && !config.database_url)}
                >
                  {isTesting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      {t.config.testing}
                    </>
                  ) : (
                    t.config.testConnection
                  )}
                </Button>
                {testResults.database === true && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <Check className="h-3 w-3 mr-1" />
                    Conectado
                  </Badge>
                )}
                {testResults.database === false && (
                  <Badge variant="destructive">
                    <X className="h-3 w-3 mr-1" />
                    Erro
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* AI Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Inteligência Artificial</CardTitle>
              <CardDescription>Configurações do provedor de IA</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>{t.config.aiProvider}</Label>
                <Select
                  value={config.llm_provider}
                  onValueChange={(value) => setConfig((prev) => ({ ...prev, llm_provider: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openrouter">OpenRouter</SelectItem>
                    <SelectItem value="ollama">Ollama</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.llm_provider === "openrouter" && (
                <div>
                  <Label>{t.config.apiKey}</Label>
                  <Input
                    type="password"
                    placeholder="sk-or-v1-..."
                    value={config.openrouter_api_key}
                    onChange={(e) => setConfig((prev) => ({ ...prev, openrouter_api_key: e.target.value }))}
                  />
                </div>
              )}

              {config.llm_provider === "ollama" && (
                <div>
                  <Label>{t.config.baseUrl}</Label>
                  <Input
                    placeholder="http://localhost:11434"
                    value={config.ollama_base_url}
                    onChange={(e) => setConfig((prev) => ({ ...prev, ollama_base_url: e.target.value }))}
                  />
                </div>
              )}

              <div>
                <Label>{t.config.defaultModel}</Label>
                <Input
                  placeholder="openai/gpt-4o-mini"
                  value={config.default_model}
                  onChange={(e) => setConfig((prev) => ({ ...prev, default_model: e.target.value }))}
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => testConnection("ai")}
                  disabled={
                    isTesting ||
                    (config.llm_provider === "openrouter" && !config.openrouter_api_key) ||
                    (config.llm_provider === "ollama" && !config.ollama_base_url)
                  }
                >
                  {isTesting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      {t.config.testing}
                    </>
                  ) : (
                    t.config.testConnection
                  )}
                </Button>
                {testResults.ai === true && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <Check className="h-3 w-3 mr-1" />
                    Conectado
                  </Badge>
                )}
                {testResults.ai === false && (
                  <Badge variant="destructive">
                    <X className="h-3 w-3 mr-1" />
                    Erro
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            {t.config.cancel}
          </Button>
          <Button onClick={saveConfig} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Salvando...
              </>
            ) : (
              t.config.save
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

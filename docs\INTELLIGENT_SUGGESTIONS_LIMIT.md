# ✨ Sistema de Limitação de Sugestões Inteligentes - Mindscape

O Mindscape agora inclui um sistema de limitação para sugestões inteligentes (ícone Sparkles) onde usuários não logados podem usar apenas 2 vezes, incentivando o cadastro para acesso ilimitado.

## 🚀 Funcionalidades

### ✅ **Limitação de Sugestões Inteligentes**
- **2 usos gratuitos**: Usuários não logados podem usar sugestões IA 2 vezes
- **Contador visual**: Banner mostra quantos usos restam em tempo real
- **Bloqueio automático**: Ícone fica desabilitado após esgotar usos
- **Modal específico**: Aparece quando usuário tenta usar após limite

### ✅ **Sistema de Conversão Inteligente**
- **Modal dedicado**: Foco específico nas sugestões de IA
- **Benefícios claros**: Destaca valor das sugestões ilimitadas
- **Call-to-action otimizado**: Botão gradiente amarelo/laranja
- **Experiência educativa**: Usuário entende o valor antes de se cadastrar

### ✅ **Experiência Premium para Usuários Logados**
- **Sugestões ilimitadas**: Sem limitação de uso das sugestões IA
- **Interface limpa**: Sem contadores ou limitações visuais
- **Acesso completo**: Todas as funcionalidades de IA disponíveis
- **Experiência fluida**: Sem interrupções ou modais de limitação

## 🔧 Como Funciona

### **1. Detecção e Rastreamento**
- **Context Provider**: `GuestLimitsProvider` gerencia estado global
- **Contador reativo**: Atualiza em tempo real conforme uso
- **Verificação automática**: Cada clique verifica disponibilidade
- **Reset para logados**: Contador reseta quando usuário faz login

### **2. Interface Adaptativa**
- **Banner informativo**: Mostra usos restantes para guests
- **Ícone visual**: Sparkles muda cor quando esgotado
- **Estado desabilitado**: Cursor e aparência indicam indisponibilidade
- **Feedback imediato**: Modal aparece instantaneamente ao tentar usar

### **3. Fluxo de Conversão**
- **Demonstração de valor**: Usuário experimenta funcionalidade
- **Limitação educativa**: Entende que há mais disponível
- **Modal persuasivo**: Destaca benefícios específicos das sugestões IA
- **Conversão direcionada**: Foco na funcionalidade que acabou de usar

## 🛠️ Arquitetura Técnica

### **Context Provider - Gerenciamento de Estado**
```typescript
interface GuestLimitsContextType {
  intelligentUsesLeft: number
  useIntelligentFeature: () => boolean
  resetLimits: () => void
  showSignupModal: () => void
}

export function GuestLimitsProvider({ children, onShowSignupModal }) {
  const [intelligentUsesLeft, setIntelligentUsesLeft] = useState(2)
  
  const useIntelligentFeature = (): boolean => {
    if (user) return true // Usuários logados têm acesso ilimitado
    
    if (intelligentUsesLeft > 0) {
      setIntelligentUsesLeft(prev => prev - 1)
      return true
    }
    
    showSignupModal() // Mostra modal quando esgotado
    return false
  }
}
```

### **Componente MindMapNode - Verificação de Uso**
```typescript
const { useIntelligentFeature, intelligentUsesLeft } = useGuestLimits()
const { user } = useAuth()

// Botão Sparkles com limitação
<circle
  stroke={!user && intelligentUsesLeft === 0 ? "#ef4444" : "#e5e7eb"}
  className={!user && intelligentUsesLeft === 0 ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
  onClick={(e) => {
    e.stopPropagation()
    if (useIntelligentFeature()) {
      // Só executa se tiver usos disponíveis
      setIconRequest({ nodeId: node.id, prompt: "..." })
    }
  }}
/>
```

### **Banner com Contador**
```typescript
export function GuestBanner() {
  const { user } = useAuth()
  const { intelligentUsesLeft } = useGuestLimits()

  if (user) return null

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      {/* ... conteúdo do banner ... */}
      <div className="flex items-center justify-center gap-2 mt-3 text-blue-700">
        <Sparkles className="h-4 w-4" />
        <span className="text-xs">
          Sugestões inteligentes restantes: <strong>{intelligentUsesLeft}</strong>
        </span>
      </div>
    </div>
  )
}
```

## 🎯 Fluxos de Uso

### **Fluxo de Usuário Não Logado**
1. **Acessa aplicação** sem fazer login
2. **Vê banner** com contador "Sugestões inteligentes restantes: 2"
3. **Gera mapa mental** e vê nós com ícones Sparkles
4. **Clica no Sparkles** pela primeira vez
5. **IA sugere conexões** e contador atualiza para "1 restante"
6. **Usa segunda vez** e contador vai para "0 restantes"
7. **Tenta usar terceira vez** e modal de limitação aparece
8. **Escolhe**: "Continuar sem IA" ou "Criar Conta Grátis"

### **Fluxo de Limitação Atingida**
1. **Usuário clica Sparkles** sem usos restantes
2. **Modal específico** aparece instantaneamente
3. **Título**: "✨ Limite de sugestões atingido"
4. **Mensagem**: "Você usou suas 2 sugestões inteligentes gratuitas!"
5. **Benefícios visuais** com ícones:
   - ✨ **Sugestões ilimitadas**: "Use IA para conectar ideias sem limites"
   - 💾 **Salvar seus mapas**: "Acesse seus mapas a qualquer momento"
   - 🧠 **Mapas ilimitados**: "Crie quantos mapas quiser, sem limitações"
   - 👥 **Compartilhar mapas**: "Colabore com outros usuários"
6. **Botões**: "Continuar sem IA" ou "Criar Conta Grátis"

### **Fluxo de Usuário Logado**
1. **Faz login** na aplicação
2. **Banner desaparece** (sem limitações)
3. **Usa Sparkles ilimitadamente** sem contadores
4. **Experiência fluida** sem interrupções
5. **Acesso completo** a todas as sugestões IA

## 📊 Interface do Usuário

### **Estados Visuais do Ícone Sparkles**

#### **Usuário Não Logado - Com Usos Disponíveis:**
```
🔵 Círculo azul normal
✨ Sparkles cinza normal
🖱️ Cursor pointer
💡 Tooltip: "Sugestões inteligentes (X restantes)"
```

#### **Usuário Não Logado - Sem Usos:**
```
🔴 Círculo vermelho
✨ Sparkles vermelho
🚫 Cursor not-allowed
👻 Opacidade 50%
```

#### **Usuário Logado:**
```
🔵 Círculo azul normal
✨ Sparkles cinza normal
🖱️ Cursor pointer
♾️ Sem limitações visuais
```

### **Banner Informativo (Guests)**
```
┌─────────────────────────────────────────────────┐
│ 🧠 Modo Demonstração                            │
│                                                 │
│ Experimente gratuitamente! Gere um mapa mental │
│ com até 8 nós.                                  │
│ Faça login ou cadastre-se para criar mapas     │
│ ilimitados e salvá-los.                         │
│                                                 │
│ ✨ Sugestões inteligentes restantes: 2          │
└─────────────────────────────────────────────────┘
```

### **Modal de Limitação**
```
┌─────────────────────────────────────────────────┐
│ ✨ Limite de sugestões atingido                 │
│                                                 │
│ ✨ Você usou suas 2 sugestões inteligentes      │
│    gratuitas!                                   │
│                                                 │
│ ✨ Sugestões ilimitadas                         │
│ 💾 Salvar seus mapas                            │
│ 🧠 Mapas ilimitados                             │
│ 👥 Compartilhar mapas                           │
│                                                 │
│ [Continuar sem IA] [Criar Conta Grátis]        │
└─────────────────────────────────────────────────┘
```

## 🔒 Limitações Implementadas

### **Para Usuários Não Logados**
- **Máximo 2 usos**: Contador decresce a cada uso
- **Bloqueio visual**: Ícone fica vermelho e desabilitado
- **Modal obrigatório**: Aparece ao tentar usar após limite
- **Reset apenas com login**: Não há como contornar sem cadastro

### **Para Usuários Logados**
- **Uso ilimitado**: Sem contadores ou limitações
- **Interface limpa**: Sem elementos visuais de limitação
- **Experiência premium**: Acesso completo às funcionalidades IA

## 🧪 Testando o Sistema

### **Teste de Limitação (Guest)**
1. **Acesse** http://localhost:3000 **sem fazer login**
2. **Observe** banner com "Sugestões inteligentes restantes: 2"
3. **Gere um mapa** com tópico qualquer
4. **Clique no ícone ✨** em qualquer nó
5. **Verifique** que contador atualiza para "1 restante"
6. **Use novamente** e veja contador ir para "0 restantes"
7. **Tente usar terceira vez** e confirme que modal aparece
8. **Teste** os botões do modal

### **Teste de Conversão**
1. **Esgote os 2 usos** como guest
2. **Clique "Criar Conta Grátis"** no modal de limitação
3. **Complete** o registro
4. **Verifique** que banner desaparece
5. **Confirme** que Sparkles funciona ilimitadamente
6. **Teste** que não há mais contadores ou limitações

### **Teste de Reset**
1. **Use sugestões** como guest
2. **Faça login** com conta existente
3. **Verifique** que limitações desaparecem
4. **Confirme** acesso ilimitado às sugestões

## 🔄 Compatibilidade

### **Integração com Outros Sistemas**
- **Sistema de mapas limitados**: Funciona junto com limitação de 8 nós
- **Modal de convite geral**: Complementa modal específico de IA
- **Autenticação**: Integra perfeitamente com sistema de login
- **Proteção por senha**: Disponível para usuários logados

### **Backward Compatibility**
- ✅ **Usuários existentes** não afetados
- ✅ **Funcionalidades antigas** inalteradas
- ✅ **APIs existentes** mantidas
- ✅ **Dados salvos** preservados

## 🚀 Deploy e Produção

### **Configurações Recomendadas**
- **Analytics**: Rastrear uso de sugestões IA por guests
- **A/B Testing**: Testar diferentes limites (1, 2, 3 usos)
- **Métricas**: Monitorar conversão específica por limitação IA
- **Feedback**: Coletar opinões sobre valor das sugestões

### **Monitoramento**
- **Uso de sugestões**: Quantas vezes guests usam antes de converter
- **Taxa de conversão IA**: % que se cadastra após esgotar sugestões
- **Tempo até conversão**: Quanto tempo entre uso e cadastro
- **Valor percebido**: Feedback sobre utilidade das sugestões

## 🎯 Benefícios do Sistema

### **Para o Negócio**
- **Demonstração de valor IA**: Usuários experimentam funcionalidade premium
- **Conversão direcionada**: Foco na funcionalidade que acabaram de usar
- **Diferenciação clara**: Mostra valor específico do cadastro
- **Educação do usuário**: Entende benefícios das sugestões IA

### **Para Usuários**
- **Experimentação guiada**: Podem testar funcionalidade premium
- **Compreensão do valor**: Veem utilidade das sugestões IA
- **Decisão informada**: Sabem exatamente o que ganham
- **Upgrade natural**: Transição suave para conta premium

### **Para o Produto**
- **Feedback de IA**: Dados sobre uso de funcionalidades inteligentes
- **Otimização de conversão**: Métricas específicas de IA
- **Segmentação avançada**: Usuários que valorizam IA vs outros
- **Desenvolvimento direcionado**: Foco em funcionalidades que convertem

---

**✨ O sistema de limitação de sugestões inteligentes está totalmente funcional e otimizado para conversão!**

### **📈 Métricas de Sucesso Esperadas**
- **Taxa de uso**: % de guests que usam sugestões IA
- **Conversão por IA**: % que se cadastra após esgotar sugestões
- **Valor percebido**: Feedback sobre utilidade das sugestões
- **Retenção**: % de usuários convertidos que continuam usando IA

-- <PERSON><PERSON><PERSON> extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON><PERSON> de configurações
CREATE TABLE IF NOT EXISTS "configuracoes" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "configuracoes_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"chave" text NOT NULL,
	"valor" text NOT NULL,
	"descricao" text,
	"criado_em" timestamp DEFAULT now() NOT NULL,
	"atualizado_em" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "configuracoes_chave_unique" UNIQUE("chave")
);

-- Tabela de mapas mentais
CREATE TABLE IF NOT EXISTS "mapas_mentais" (
	"id" text PRIMARY KEY NOT NULL,
	"nome" text NOT NULL,
	"descricao" text,
	"nos" jsonb NOT NULL,
	"conexoes" jsonb NOT NULL,
	"protegido_senha" boolean DEFAULT false NOT NULL,
	"hash_senha" text,
	"tags" jsonb DEFAULT '[]'::jsonb,
	"metadados" jsonb DEFAULT '{}'::jsonb,
	"versao" integer DEFAULT 1 NOT NULL,
	"criado_em" timestamp DEFAULT now() NOT NULL,
	"atualizado_em" timestamp DEFAULT now() NOT NULL,
	"acessado_em" timestamp DEFAULT now()
);

-- Tabela de sessões
CREATE TABLE IF NOT EXISTS "sessoes" (
	"id" text PRIMARY KEY NOT NULL,
	"dados_sessao" jsonb NOT NULL,
	"expires_em" timestamp NOT NULL,
	"criado_em" timestamp DEFAULT now() NOT NULL
);

-- Tabela de logs de auditoria
CREATE TABLE IF NOT EXISTS "logs_auditoria" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "logs_auditoria_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"acao" text NOT NULL,
	"recurso" text NOT NULL,
	"recurso_id" text,
	"dados_antigos" jsonb,
	"dados_novos" jsonb,
	"metadados" jsonb DEFAULT '{}'::jsonb,
	"criado_em" timestamp DEFAULT now() NOT NULL
);

-- Tabela de backups
CREATE TABLE IF NOT EXISTS "backups" (
	"id" text PRIMARY KEY NOT NULL,
	"tipo" text NOT NULL,
	"dados" jsonb NOT NULL,
	"tamanho" integer,
	"checksum" text,
	"metadados" jsonb DEFAULT '{}'::jsonb,
	"criado_em" timestamp DEFAULT now() NOT NULL
);

-- Tabela de métricas
CREATE TABLE IF NOT EXISTS "metricas" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "metricas_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"operacao" text NOT NULL,
	"tempo_execucao" integer NOT NULL,
	"sucesso" boolean NOT NULL,
	"erro" text,
	"metadados" jsonb DEFAULT '{}'::jsonb,
	"criado_em" timestamp DEFAULT now() NOT NULL
);

-- Criar índices
CREATE UNIQUE INDEX IF NOT EXISTS "configuracoes_chave_idx" ON "configuracoes" ("chave");
CREATE INDEX IF NOT EXISTS "configuracoes_atualizado_em_idx" ON "configuracoes" ("atualizado_em");

CREATE INDEX IF NOT EXISTS "mapas_mentais_nome_idx" ON "mapas_mentais" ("nome");
CREATE INDEX IF NOT EXISTS "mapas_mentais_criado_em_idx" ON "mapas_mentais" ("criado_em");
CREATE INDEX IF NOT EXISTS "mapas_mentais_atualizado_em_idx" ON "mapas_mentais" ("atualizado_em");
CREATE INDEX IF NOT EXISTS "mapas_mentais_protegido_senha_idx" ON "mapas_mentais" ("protegido_senha");
CREATE INDEX IF NOT EXISTS "mapas_mentais_tags_idx" ON "mapas_mentais" USING gin ("tags");

CREATE INDEX IF NOT EXISTS "sessoes_expires_em_idx" ON "sessoes" ("expires_em");

CREATE INDEX IF NOT EXISTS "logs_auditoria_acao_idx" ON "logs_auditoria" ("acao");
CREATE INDEX IF NOT EXISTS "logs_auditoria_recurso_idx" ON "logs_auditoria" ("recurso");
CREATE INDEX IF NOT EXISTS "logs_auditoria_criado_em_idx" ON "logs_auditoria" ("criado_em");

CREATE INDEX IF NOT EXISTS "backups_tipo_idx" ON "backups" ("tipo");
CREATE INDEX IF NOT EXISTS "backups_criado_em_idx" ON "backups" ("criado_em");

CREATE INDEX IF NOT EXISTS "metricas_operacao_idx" ON "metricas" ("operacao");
CREATE INDEX IF NOT EXISTS "metricas_sucesso_idx" ON "metricas" ("sucesso");
CREATE INDEX IF NOT EXISTS "metricas_criado_em_idx" ON "metricas" ("criado_em");

-- Inserir configurações padrão
INSERT INTO "configuracoes" ("chave", "valor", "descricao") VALUES 
('llm_provider', 'openrouter', 'Provedor de IA (openrouter/ollama)'),
('openrouter_api_key', '', 'Chave da API do OpenRouter'),
('ollama_base_url', 'http://localhost:11434', 'URL base do servidor Ollama'),
('modelo_padrao', 'openai/gpt-4o', 'Modelo de IA padrão'),
('tema', 'claro', 'Tema da interface (claro/escuro)'),
('auto_salvar', 'true', 'Salvar automaticamente a cada 5 minutos'),
('zoom_padrao', '1', 'Nível de zoom padrão'),
('grade_visivel', 'true', 'Mostrar grade no canvas')
ON CONFLICT ("chave") DO NOTHING;

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.atualizado_em = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar timestamps
CREATE TRIGGER update_configuracoes_updated_at 
    BEFORE UPDATE ON configuracoes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mapas_mentais_updated_at 
    BEFORE UPDATE ON mapas_mentais 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

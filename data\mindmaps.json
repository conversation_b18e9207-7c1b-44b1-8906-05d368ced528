[{"id": "ef6ad5fd-36bd-40ec-b2f8-890247b294f6", "name": "mapas mentais", "data": {"nodes": [{"id": "root", "content": "mapas mentais", "x": 454, "y": 298, "level": 0, "color": "#f97316"}, {"id": "mrFBsqcJAYX_psmir2vgJ", "content": "Processos cognitivos", "x": 1118, "y": 389, "level": 1, "color": "#6366f1", "icon": "🧠"}, {"id": "80O5mvk7l-C9eDxdOH2Bo", "content": "Memória e associação", "x": 663.2792206135786, "y": 516.2792206135786, "level": 1, "icon": "🔗"}, {"id": "ds9rp73hThB8iTTD4vwTb", "content": "Aprendizado visual", "x": 412, "y": 535, "level": 1, "icon": "👁️"}, {"id": "dvA9uwTx-RAqC33iWOJ9_", "content": "Estrutura hierár<PERSON>", "x": 191.72077938642144, "y": 437.27922061357856, "level": 1, "icon": "📊"}, {"id": "hjp1j3S57Ok5wPGCh_eLN", "content": "Criatividade e brainstorming", "x": 174, "y": 242, "level": 1, "icon": "💡"}, {"id": "8ThSoCXZKVGcyWrxh_ns1", "content": "Gestão de conhecimento", "x": 249.72077938642144, "y": 80.72077938642144, "level": 1, "icon": "📚"}, {"id": "su4E3_j8gV6V9SOJjuBVT", "content": "Ferramentas digitais", "x": 554, "y": 52, "level": 1, "icon": "💻"}, {"id": "aLFN5UV424oV-Hu0wJCGk", "content": "Tomada de decisão", "x": 786.2792206135786, "y": 208.7207793864214, "level": 1, "icon": "⚖️"}, {"id": "ggaSNGHW2ERiQ4TdkUG5O", "content": "Codificação neural", "x": 1311, "y": 400, "level": 2, "icon": "🧠"}, {"id": "EhzE5wbdss2VmqWeESABI", "content": "Pa<PERSON><PERSON><PERSON> visuais", "x": 1245, "y": 551.884572681199, "level": 2, "icon": "👁️"}, {"id": "y-o2reAJBIsf5ejfXKUeq", "content": "Pensamento divergente", "x": 1004, "y": 574.884572681199, "level": 2, "icon": "💡"}, {"id": "iKhhAjRtHm5XexxOZx_dB", "content": "Heurísticas mentais", "x": 1166, "y": 89, "level": 2, "icon": "⚖️"}, {"id": "VNTEmKG2P3hWQCZ6dbn4l", "content": "Metacognição", "x": 1050, "y": 213.11542731880107, "level": 2, "icon": "🔄"}, {"id": "VwbUl9Cc3FWfePputHt_S", "content": "Viés cognitivo", "x": 1254, "y": 239.11542731880104, "level": 2, "icon": "🎭"}], "connections": [{"from": "root", "to": "mrFBsqcJAYX_psmir2vgJ"}, {"from": "root", "to": "80O5mvk7l-C9eDxdOH2Bo"}, {"from": "root", "to": "ds9rp73hThB8iTTD4vwTb"}, {"from": "root", "to": "dvA9uwTx-RAqC33iWOJ9_"}, {"from": "root", "to": "hjp1j3S57Ok5wPGCh_eLN"}, {"from": "root", "to": "8ThSoCXZKVGcyWrxh_ns1"}, {"from": "root", "to": "su4E3_j8gV6V9SOJjuBVT"}, {"from": "root", "to": "aLFN5UV424oV-Hu0wJCGk"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "ggaSNGHW2ERiQ4TdkUG5O"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "EhzE5wbdss2VmqWeESABI"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "y-o2reAJBIsf5ejfXKUeq"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "iKhhAjRtHm5XexxOZx_dB"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "VNTEmKG2P3hWQCZ6dbn4l"}, {"from": "mrFBsqcJAYX_psmir2vgJ", "to": "VwbUl9Cc3FWfePputHt_S"}]}, "is_protected": false, "created_at": "2025-07-27T21:49:52.878Z", "updated_at": "2025-07-27T22:00:40.537Z"}]
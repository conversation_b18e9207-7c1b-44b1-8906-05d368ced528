import { neon } from "@neondatabase/serverless"
import { drizzle } from "drizzle-orm/neon-http"
import { migrate } from "drizzle-orm/neon-http/migrator"
import * as schema from "./schema"

// Configuração da conexão com Neon
const isDevelopment = process.env.NODE_ENV === "development"
const isVercel = process.env.VERCEL === "1"

// Obter string de conexão baseada no ambiente
function getDatabaseUrl(): string {
  // Prioridade: variável específica do ambiente > variável geral > fallback
  const databaseUrl = process.env.NEON_DATABASE_URL || process.env.DATABASE_URL || process.env.POSTGRES_URL

  if (!databaseUrl) {
    throw new Error(`
      ❌ String de conexão do banco não encontrada!
      
      Configure uma das seguintes variáveis de ambiente:
      - NEON_DATABASE_URL (recomendado)
      - DATABASE_URL
      - POSTGRES_URL
      
      Exemplo: ************************************/database?sslmode=require
    `)
  }

  return databaseUrl
}

// Criar conexão SQL com Neon
const sql = neon(getDatabaseUrl())

// Criar instância do Drizzle ORM
export const db = drizzle(sql, { schema })

// Função para testar conexão
export async function testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
  try {
    console.log("🔍 Testando conexão com Neon...")

    const result = await sql`SELECT version(), current_database(), current_user, now() as timestamp`

    console.log("✅ Conexão com Neon estabelecida!")

    return {
      success: true,
      message: "Conexão com Neon estabelecida com sucesso!",
      details: {
        version: result[0].version,
        database: result[0].current_database,
        user: result[0].current_user,
        timestamp: result[0].timestamp,
        environment: process.env.NODE_ENV,
        vercel: isVercel,
      },
    }
  } catch (error) {
    console.error("❌ Erro ao conectar com Neon:", error)

    return {
      success: false,
      message: `Erro na conexão: ${error}`,
      details: {
        error: error instanceof Error ? error.message : "Erro desconhecido",
        environment: process.env.NODE_ENV,
        vercel: isVercel,
      },
    }
  }
}

// Função para executar migrações
export async function runMigrations(): Promise<{ success: boolean; message: string }> {
  try {
    console.log("🔄 Executando migrações do banco...")

    await migrate(db, { migrationsFolder: "./drizzle/migrations" })

    console.log("✅ Migrações executadas com sucesso!")

    return {
      success: true,
      message: "Migrações executadas com sucesso!",
    }
  } catch (error) {
    console.error("❌ Erro ao executar migrações:", error)

    return {
      success: false,
      message: `Erro nas migrações: ${error}`,
    }
  }
}

// Função para verificar saúde do banco
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean
  message: string
  metrics: {
    connectionTime: number
    tablesCount: number
    mindMapsCount: number
    configurationsCount: number
    lastBackup?: string
  }
}> {
  const startTime = Date.now()

  try {
    // Testar conexão básica
    await sql`SELECT 1`

    // Obter métricas do banco
    const [tablesResult] = await sql`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `

    const [mindMapsResult] = await sql`
      SELECT COUNT(*) as count FROM mapas_mentais
    `

    const [configResult] = await sql`
      SELECT COUNT(*) as count FROM configuracoes
    `

    const connectionTime = Date.now() - startTime

    return {
      healthy: true,
      message: `Banco saudável - ${connectionTime}ms de latência`,
      metrics: {
        connectionTime,
        tablesCount: Number(tablesResult.count),
        mindMapsCount: Number(mindMapsResult.count),
        configurationsCount: Number(configResult.count),
      },
    }
  } catch (error) {
    return {
      healthy: false,
      message: `Erro no banco: ${error}`,
      metrics: {
        connectionTime: Date.now() - startTime,
        tablesCount: 0,
        mindMapsCount: 0,
        configurationsCount: 0,
      },
    }
  }
}

// Função para backup de dados
export async function createBackup(): Promise<{ success: boolean; message: string; backupId?: string }> {
  try {
    const backupId = `backup_${Date.now()}`

    // Criar tabela de backup se não existir
    await sql`
      CREATE TABLE IF NOT EXISTS backups (
        id TEXT PRIMARY KEY,
        created_at TIMESTAMP DEFAULT NOW(),
        data JSONB NOT NULL,
        type TEXT NOT NULL
      )
    `

    // Fazer backup dos mapas mentais
    const mindMaps = await sql`SELECT * FROM mapas_mentais`
    const configurations = await sql`SELECT * FROM configuracoes`

    const backupData = {
      mindMaps,
      configurations,
      metadata: {
        version: "1.0.0",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
      },
    }

    await sql`
      INSERT INTO backups (id, data, type)
      VALUES (${backupId}, ${JSON.stringify(backupData)}, 'full')
    `

    return {
      success: true,
      message: `Backup criado com sucesso: ${backupId}`,
      backupId,
    }
  } catch (error) {
    console.error("❌ Erro ao criar backup:", error)
    return {
      success: false,
      message: `Erro ao criar backup: ${error}`,
    }
  }
}

// Função para restaurar backup
export async function restoreBackup(backupId: string): Promise<{ success: boolean; message: string }> {
  try {
    const [backup] = await sql`
      SELECT data FROM backups WHERE id = ${backupId}
    `

    if (!backup) {
      return {
        success: false,
        message: "Backup não encontrado",
      }
    }

    const backupData = backup.data as any

    // Restaurar configurações
    await sql`DELETE FROM configuracoes`
    for (const config of backupData.configurations) {
      await sql`
        INSERT INTO configuracoes (chave, valor, descricao, criado_em, atualizado_em)
        VALUES (${config.chave}, ${config.valor}, ${config.descricao}, ${config.criado_em}, ${config.atualizado_em})
      `
    }

    // Restaurar mapas mentais
    await sql`DELETE FROM mapas_mentais`
    for (const map of backupData.mindMaps) {
      await sql`
        INSERT INTO mapas_mentais (id, nome, descricao, nos, conexoes, protegido_senha, hash_senha, criado_em, atualizado_em)
        VALUES (${map.id}, ${map.nome}, ${map.descricao}, ${map.nos}, ${map.conexoes}, ${map.protegido_senha}, ${map.hash_senha}, ${map.criado_em}, ${map.atualizado_em})
      `
    }

    return {
      success: true,
      message: `Backup ${backupId} restaurado com sucesso!`,
    }
  } catch (error) {
    console.error("❌ Erro ao restaurar backup:", error)
    return {
      success: false,
      message: `Erro ao restaurar backup: ${error}`,
    }
  }
}

// Pool de conexões para otimização
class ConnectionPool {
  private connections: Map<string, any> = new Map()
  private maxConnections = 10

  async getConnection(key = "default") {
    if (!this.connections.has(key)) {
      if (this.connections.size >= this.maxConnections) {
        // Remover conexão mais antiga
        const firstKey = this.connections.keys().next().value
        this.connections.delete(firstKey)
      }

      this.connections.set(key, neon(getDatabaseUrl()))
    }

    return this.connections.get(key)
  }

  closeAll() {
    this.connections.clear()
  }
}

export const connectionPool = new ConnectionPool()

// Middleware para retry automático
export async function withRetry<T>(operation: () => Promise<T>, maxRetries = 3, delay = 1000): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxRetries) {
        throw lastError
      }

      console.warn(`⚠️ Tentativa ${attempt} falhou, tentando novamente em ${delay}ms...`)
      await new Promise((resolve) => setTimeout(resolve, delay * attempt))
    }
  }

  throw lastError!
}

// Função para monitoramento de performance
export class DatabaseMetrics {
  private static metrics: Map<string, { count: number; totalTime: number; errors: number }> = new Map()

  static async measure<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now()

    try {
      const result = await fn()
      this.recordSuccess(operation, Date.now() - startTime)
      return result
    } catch (error) {
      this.recordError(operation, Date.now() - startTime)
      throw error
    }
  }

  private static recordSuccess(operation: string, time: number) {
    const metric = this.metrics.get(operation) || { count: 0, totalTime: 0, errors: 0 }
    metric.count++
    metric.totalTime += time
    this.metrics.set(operation, metric)
  }

  private static recordError(operation: string, time: number) {
    const metric = this.metrics.get(operation) || { count: 0, totalTime: 0, errors: 0 }
    metric.errors++
    this.metrics.set(operation, metric)
  }

  static getMetrics() {
    const result: any = {}

    for (const [operation, metric] of this.metrics.entries()) {
      result[operation] = {
        count: metric.count,
        averageTime: metric.count > 0 ? metric.totalTime / metric.count : 0,
        errors: metric.errors,
        successRate: metric.count > 0 ? ((metric.count - metric.errors) / metric.count) * 100 : 0,
      }
    }

    return result
  }

  static reset() {
    this.metrics.clear()
  }
}

# 🔐 Sistema de Proteção por Senha - Mindscape

O Mindscape agora inclui um sistema completo de proteção por senha para mapas mentais, permitindo que usuários protejam seus mapas com senhas individuais.

## 🚀 Funcionalidades

### ✅ **Proteção Individual por Mapa**
- **Proteção opcional**: Cada mapa pode ser protegido individualmente
- **Senhas únicas**: Cada mapa protegido tem sua própria senha
- **Criptografia segura**: Senhas criptografadas com bcrypt (12 rounds)
- **Controle total**: Proprietário pode definir, alterar ou remover proteção

### ✅ **Interface Intuitiva**
- **Indicadores visuais**: Ícone de cadeado para mapas protegidos
- **Botões contextuais**: Shield/ShieldOff baseados no status de proteção
- **Modal unificado**: Interface única para todas as operações de senha
- **Feedback imediato**: Toasts de sucesso/erro para todas as ações

### ✅ **Fluxo de Segurança**
- **Acesso controlado**: Mapas protegidos exigem senha para visualização
- **Dados ocultos**: Conteúdo não é retornado sem autenticação
- **Verificação automática**: Sistema verifica proteção ao abrir mapas
- **Sessão temporária**: Acesso liberado apenas durante a sessão atual

## 🔧 Como Usar

### **1. Proteger um Mapa Mental**
1. Vá para **"Meus Mapas"**
2. Clique no ícone **🛡️** (Shield) no mapa desejado
3. Digite uma **senha forte**
4. Clique em **"Proteger Mapa"**
5. O mapa agora está protegido (ícone 🔒 aparece)

### **2. Acessar Mapa Protegido**
1. Clique em **"Editar"** em um mapa protegido
2. Modal de senha aparece automaticamente
3. Digite a **senha correta**
4. Clique em **"Desbloquear"**
5. Mapa abre normalmente no editor

### **3. Remover Proteção**
1. Clique no ícone **🛡️⚠️** (ShieldOff) no mapa protegido
2. Confirme a remoção na tela de aviso
3. Clique em **"Remover Proteção"**
4. Mapa volta a ser público (sem senha)

## 🛠️ Arquitetura Técnica

### **APIs de Proteção por Senha**
```
POST /api/mindmaps/[id]/password    - Definir proteção por senha
DELETE /api/mindmaps/[id]/password  - Remover proteção por senha
POST /api/mindmaps/[id]/unlock      - Verificar senha e desbloquear
GET /api/mindmaps/[id]              - Retorna dados limitados se protegido
```

### **Banco de Dados**
- **Campos adicionados** ao MindMap:
```typescript
interface MindMap {
  // ... campos existentes
  is_protected?: boolean     // Se o mapa está protegido
  password_hash?: string     // Hash da senha (bcrypt)
}
```

### **Segurança Implementada**
```typescript
// Criptografia da senha
const passwordHash = await bcrypt.hash(password, 12)

// Verificação da senha
const isValid = await bcrypt.compare(password, storedHash)

// Proteção de dados
if (mindMap.is_protected) {
  return { ...mindMap, dados: { nodes: [], connections: [] } }
}
```

## 🎯 Fluxos de Uso

### **Fluxo de Proteção**
1. **Usuário** seleciona mapa para proteger
2. **Sistema** abre modal de definição de senha
3. **Usuário** digita senha forte
4. **Sistema** criptografa e salva senha
5. **Mapa** fica protegido com indicador visual

### **Fluxo de Acesso**
1. **Usuário** tenta abrir mapa protegido
2. **Sistema** detecta proteção e bloqueia dados
3. **Modal** de senha aparece automaticamente
4. **Usuário** digita senha correta
5. **Sistema** verifica e libera acesso ao mapa

### **Fluxo de Remoção**
1. **Usuário** clica em remover proteção
2. **Sistema** mostra aviso de confirmação
3. **Usuário** confirma remoção
4. **Sistema** remove proteção e hash da senha
5. **Mapa** volta a ser acessível sem senha

## 📊 Interface do Usuário

### **Indicadores Visuais**
```tsx
// Título do mapa com indicador
<CardTitle className="flex items-center gap-2">
  {mindMap.titulo}
  {mindMap.is_protected && (
    <Lock className="h-4 w-4 text-orange-600" />
  )}
</CardTitle>

// Botão de proteção contextual
<Button className={mindMap.is_protected ? "text-orange-600" : "text-green-600"}>
  {mindMap.is_protected ? <ShieldOff /> : <Shield />}
</Button>
```

### **Modal de Proteção**
- **Modo 'set'**: Definir nova senha
- **Modo 'unlock'**: Inserir senha para desbloquear
- **Modo 'remove'**: Confirmar remoção de proteção

### **Estados do Sistema**
- **🛡️ Verde**: Mapa não protegido (pode proteger)
- **🛡️⚠️ Laranja**: Mapa protegido (pode remover proteção)
- **🔒 Laranja**: Indicador de mapa protegido no título

## 🔒 Segurança e Validação

### **Criptografia de Senhas**
```typescript
// Configuração bcrypt
const saltRounds = 12  // Alto nível de segurança
const passwordHash = await bcrypt.hash(password, saltRounds)
```

### **Validação de Entrada**
```typescript
const passwordSchema = z.object({
  password: z.string().min(1, 'Senha é obrigatória')
})
```

### **Verificações de Segurança**
- **Propriedade**: Só o dono pode proteger/desproteger
- **Existência**: Verifica se o mapa existe antes de proteger
- **Autenticação**: Requer usuário logado para operações
- **Autorização**: Valida permissões antes de executar ações

### **Proteção de Dados**
```typescript
// API GET retorna dados limitados para mapas protegidos
if (mindMap.is_protected) {
  return {
    ...mindMap,
    dados: { nodes: [], connections: [] }, // Dados vazios
    isProtected: true,
    message: "Este mapa mental está protegido por senha"
  }
}
```

## 🔄 Compatibilidade

### **Backward Compatibility**
- ✅ **Mapas existentes** continuam funcionando normalmente
- ✅ **Sem proteção por padrão**: Mapas criados são públicos
- ✅ **Migração suave**: Campos opcionais não quebram dados existentes
- ✅ **Funcionalidades antigas** inalteradas

### **Integração com Outros Sistemas**
- **Autenticação**: Requer login para proteger mapas
- **Compartilhamento**: Mapas protegidos podem ser compartilhados
- **Colaboração**: Usuários com acesso precisam da senha
- **Exportação**: Mapas protegidos podem ser exportados após desbloqueio

## 🚀 Deploy e Produção

### **Configurações de Segurança**
- **bcrypt rounds**: 12 (configurável via código)
- **Validação**: Schemas Zod para entrada
- **Logs**: Tentativas de acesso registradas
- **Backup**: Hashes de senha incluídos no backup

### **Monitoramento**
- **Tentativas de acesso**: Logs de verificação de senha
- **Mapas protegidos**: Contagem de mapas com proteção
- **Falhas de autenticação**: Senhas incorretas registradas

## 🧪 Testando o Sistema

### **Cenários de Teste**

#### **Proteção de Mapa**
```bash
# 1. Proteger mapa (requer autenticação)
curl -X POST http://localhost:3000/api/mindmaps/MAP_ID/password \
  -H "Content-Type: application/json" \
  -H "Cookie: auth-token=TOKEN" \
  -d '{"password":"minhasenha123"}'
```

#### **Acesso a Mapa Protegido**
```bash
# 2. Tentar acessar mapa protegido (dados limitados)
curl http://localhost:3000/api/mindmaps/MAP_ID

# 3. Desbloquear mapa com senha
curl -X POST http://localhost:3000/api/mindmaps/MAP_ID/unlock \
  -H "Content-Type: application/json" \
  -d '{"password":"minhasenha123"}'
```

#### **Remoção de Proteção**
```bash
# 4. Remover proteção (requer autenticação)
curl -X DELETE http://localhost:3000/api/mindmaps/MAP_ID/password \
  -H "Cookie: auth-token=TOKEN"
```

### **Casos de Teste**
1. **✅ Proteger mapa próprio** - Deve funcionar
2. **❌ Proteger mapa de outro usuário** - Deve falhar (403)
3. **✅ Acessar mapa protegido com senha correta** - Deve funcionar
4. **❌ Acessar mapa protegido com senha incorreta** - Deve falhar (401)
5. **✅ Remover proteção própria** - Deve funcionar
6. **❌ Tentar proteger sem autenticação** - Deve falhar (401)

## 🎯 Benefícios do Sistema

### **Para Usuários**
- **Privacidade**: Controle total sobre acesso aos mapas
- **Flexibilidade**: Proteção opcional e reversível
- **Simplicidade**: Interface intuitiva e fácil de usar
- **Segurança**: Criptografia forte e validações robustas

### **Para Desenvolvedores**
- **Modular**: Sistema independente e bem encapsulado
- **Extensível**: Fácil de adicionar novas funcionalidades
- **Testável**: APIs bem definidas e testáveis
- **Manutenível**: Código limpo e bem documentado

---

**🔐 O sistema de proteção por senha está totalmente funcional e pronto para uso seguro!**

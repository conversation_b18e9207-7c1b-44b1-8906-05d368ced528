========================================
DEPLOY DO MINDSCAPE - INSTRUÇÕES SIMPLES
========================================

SERVIDOR: **************
USUÁRIO: usuario
SENHA: Senh@01020304
PORTA: 3030

========================================
1. UPLOAD DO ARQUIVO
========================================

1.1. Baixe e instale WinSCP: https://winscp.net/
1.2. Abra WinSCP e configure:
     - Host: **************
     - Usuário: usuario
     - Senha: Senh@01020304
     - Porta: 22
1.3. Conecte ao servidor
1.4. Navegue até /home/<USER>/
1.5. <PERSON><PERSON> a pasta "mindscape-app" se não existir
1.6. Faça upload do arquivo "mindscape-deploy.tar.gz" para /home/<USER>/mindscape-app/

========================================
2. CONECTAR AO SERVIDOR
========================================

2.1. Baixe e instale PuTTY: https://www.putty.org/
2.2. Abra PuTTY e configure:
     - Host: **************
     - Porta: 22
2.3. Clique "Open"
2.4. Login: usuario
2.5. Senha: Senh@01020304

========================================
3. COMANDOS PARA EXECUTAR NO SERVIDOR
========================================

Copie e cole cada comando abaixo no terminal SSH:

# Ir para o diretório
cd /home/<USER>/mindscape-app

# Atualizar sistema
sudo apt-get update -y

# Instalar Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar versões
node --version
npm --version

# Instalar PM2
sudo npm install -g pm2

# Extrair aplicação
tar -xzf mindscape-deploy.tar.gz
rm mindscape-deploy.tar.gz

# Instalar dependências
npm install --production

# Fazer build
npm run build

# Criar diretório de logs
mkdir -p logs

# Parar aplicação existente (se houver)
pm2 stop mindscape || true
pm2 delete mindscape || true

# Iniciar aplicação na porta 3030
pm2 start npm --name "mindscape" -- start

# Salvar configuração
pm2 save

# Verificar status
pm2 status

# Ver logs
pm2 logs mindscape --lines 20

# Testar aplicação localmente
curl http://localhost:3030

# Configurar firewall
sudo ufw allow 3030
sudo ufw status

========================================
4. VERIFICAR APLICAÇÃO
========================================

Após executar todos os comandos, acesse:
http://**************:3030

========================================
5. COMANDOS DE MONITORAMENTO
========================================

# Ver status da aplicação
pm2 status

# Ver logs em tempo real
pm2 logs mindscape

# Reiniciar aplicação
pm2 restart mindscape

# Parar aplicação
pm2 stop mindscape

# Iniciar aplicação
pm2 start mindscape

# Monitor de recursos
pm2 monit

# Ver informações detalhadas
pm2 show mindscape

========================================
6. TROUBLESHOOTING
========================================

Se a aplicação não funcionar:

1. Verificar logs:
   pm2 logs mindscape

2. Verificar se a porta está livre:
   sudo lsof -i :3030

3. Reiniciar aplicação:
   pm2 restart mindscape

4. Testar localmente:
   curl http://localhost:3030

5. Verificar processo:
   pm2 status

6. Verificar firewall:
   sudo ufw status

7. Se necessário, matar processo na porta:
   sudo kill -9 $(sudo lsof -t -i:3030)

========================================
7. LOGS DA APLICAÇÃO
========================================

Os logs ficam em:
- Erro: /home/<USER>/mindscape-app/logs/err.log
- Saída: /home/<USER>/mindscape-app/logs/out.log
- Combinado: /home/<USER>/mindscape-app/logs/combined.log

Para ver logs:
tail -f /home/<USER>/mindscape-app/logs/combined.log

========================================
8. CONFIGURAÇÃO ADICIONAL
========================================

Para configurar auto-start no boot:
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u usuario --hp /home/<USER>

Para salvar configuração atual:
pm2 save

========================================
9. VERIFICAÇÃO FINAL
========================================

1. Verificar se Node.js está instalado:
   node --version (deve ser 18+)

2. Verificar se PM2 está instalado:
   pm2 --version

3. Verificar se aplicação está rodando:
   pm2 status

4. Verificar se porta está aberta:
   sudo ufw status

5. Testar aplicação:
   curl http://localhost:3030

6. Acessar externamente:
   http://**************:3030

========================================
SUCESSO!
========================================

Se todos os passos foram executados corretamente,
sua aplicação Mindscape estará rodando em:

http://**************:3030

Funcionalidades disponíveis:
- Criação de mapas mentais com IA
- Sistema de usuários e autenticação
- Compartilhamento de mapas
- Proteção por senha
- Sugestões inteligentes
- Interface responsiva

========================================

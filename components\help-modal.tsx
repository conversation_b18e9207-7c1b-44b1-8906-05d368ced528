"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { HelpCircle, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import dynamic from "next/dynamic"

const ScrollLink = dynamic(async () => {
  const mod = await import("react-scroll")
  return mod.Link
}, { ssr: false })

interface HelpModalProps {
  isOpen: boolean
  onClose: () => void
}

export function HelpModal({ isOpen, onClose }: HelpModalProps) {
  const [content, setContent] = useState("")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [toc, setToc] = useState<Array<{ id: string; text: string }>>([])

  useEffect(() => {
    if (isOpen) {
      const fetchHelpContent = async () => {
        setLoading(true)
        setError(null)
        try {
          const response = await fetch("/api/help")
          if (!response.ok) {
            throw new Error("Falha ao carregar o conteúdo da ajuda.")
          }
          const data = await response.json()
          // adiciona ids aos títulos para navegação
          setContent(data.content)
          const lines = data.content.split("\n")
          const table = lines
            .filter((l: string) => /^###?\s/.test(l))
            .map((l: string) => {
              const text = l.replace(/^#+\s/, "").trim()
              const id = text.toLowerCase().replace(/[^a-z0-9]+/g, "-")
              return { id, text }
            })
          setToc(table)
        } catch (err) {
          setError(err instanceof Error ? err.message : "Ocorreu um erro desconhecido.")
        } finally {
          setLoading(false)
        }
      }

      fetchHelpContent()
    }
  }, [isOpen])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-4 py-2 rounded shadow">
            <HelpCircle className="w-6 h-6" />
            Central de Ajuda
          </DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-hidden flex">
          {/* Índice */}
          {toc.length > 0 && (
            <aside className="w-48 border-r pr-3 mr-3 overflow-y-auto text-sm" >
              <p className="font-semibold mb-2">Conteúdo</p>
              <ul className="space-y-1">
                {toc.map((item) => (
                  <li key={item.id}>
                    <ScrollLink
                      to={item.id}
                      smooth={true}
                      offset={-80}
                      containerId="help-scroll"
                      spy={true}
                      activeClass="font-bold text-indigo-600"
                      className="cursor-pointer text-blue-600 hover:underline"
                    >
                      {item.text}
                    </ScrollLink>
                  </li>
                ))}
              </ul>
            </aside>
          )}

          <ScrollArea className="h-full flex-1 pr-6" id="help-scroll">
            {loading && (
              <div className="flex flex-col items-center justify-center h-full">
                <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                <p className="mt-4 text-gray-500">Carregando conteúdo...</p>
              </div>
            )}
            {error && (
              <div className="flex flex-col items-center justify-center h-full text-red-500">
                <AlertTriangle className="w-8 h-8" />
                <p className="mt-4">{error}</p>
              </div>
            )}
            {!loading && !error && (
              <article className="prose prose-blue max-w-none dark:prose-invert" id="help-markdown">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h2: ({ node, ...props }) => {
                      const text = String(props.children)
                      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, "-")
                      return <h2 id={id} {...props} />
                    },
                    h3: ({ node, ...props }) => {
                      const text = String(props.children)
                      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, "-")
                      return <h3 id={id} {...props} />
                    },
                  }}
                >
                  {content}
                </ReactMarkdown>
              </article>
            )}
          </ScrollArea>
        </div>
        <div className="flex justify-end pt-4 border-t">
          <Button onClick={onClose}>Fechar</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

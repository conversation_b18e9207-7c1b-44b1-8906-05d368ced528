# Guia Completo do Mind Map IA

Bem-vindo ao guia completo do **Mind Map IA**. Este documento serve como uma referência central para todas as funcionalidades, configurações e melhores práticas ao usar a aplicação.

## 1. Introdução

O Mapa Mental IA é uma ferramenta poderosa que combina a criatividade dos mapas mentais tradicionais com a inteligência artificial para ajudá-lo a organizar, explorar e conectar suas ideias de forma visual e intuitiva.

### 🎯 O que você pode fazer:

*   **Criar Mapas Mentais:** Construa mapas mentais interativos com nós e conexões de forma manual ou automática.
*   **Geração com IA:** Utilize a IA para gerar mapas completos sobre qualquer tópico ou para expandir ideias existentes através de um chat interativo.
*   **Organização Visual:** Personalize seus mapas com cores, tamanhos e um layout livre no canvas.
*   **Armazenamento Local:** Salve seus mapas mentais de forma segura no seu dispositivo.
*   **Provedores de IA:** Conecte-se a serviços como OpenRouter (online) ou Ollama (local) para alimentar a geração de conteúdo.
*   **Exportação:** Exporte seus mapas mentais para os formatos Markdown (.md) e PDF.

---

## 2. Diagrama de Arquitetura da Aplicação

O diagrama abaixo ilustra a interação entre os principais componentes da aplicação, mostrando o fluxo de dados e controle.

```mermaid
graph TD
    subgraph "Interface do Usuário (UI)"
        A[app/page.tsx] --> B{Verifica Configuração};
        B -- "Não Configurado" --> C[SetupWizard];
        B -- "Configurado" --> D[MainApp];
        C -- "Salva Configs" --> E[API: /api/config];
        D --> F[MindMapEditor];
        D --> G[ConfigModal];
        D --> H[HelpModal];
    end

    subgraph "Componentes Principais"
        F -- "Edita/Visualiza" --> I[MindMapCanvas];
        D -- "Ações de Mapa" --> J[API: /api/mindmaps];
        D -- "Geração IA" --> K[API: /api/chat];
        G -- "Salva Configs" --> E;
    end

    subgraph "Backend (API Routes)"
        J -- "Operações CRUD" --> L[lib/database.ts];
        K -- "Chama IA" --> M[lib/llm-service.ts];
        E -- "Salva/Lê" --> L;
    end

    subgraph "Serviços e Bibliotecas"
        M -- "Conecta" --> N[Provedores IA: OpenRouter/Ollama];
        L -- "Persistência" --> O[SQLite (mindmap.db)];
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#cfc,stroke:#333,stroke-width:2px
    style K fill:#cfc,stroke:#333,stroke-width:2px
    style E fill:#cfc,stroke:#333,stroke-width:2px
    style L fill:#ff9,stroke:#333,stroke-width:2px
    style M fill:#ff9,stroke:#333,stroke-width:2px
    style N fill:#f99,stroke:#333,stroke-width:2px
    style O fill:#f99,stroke:#333,stroke-width:2px
```

---

## 3. Funcionalidades Principais

### Gerador de Mapas Mentais (Tela Principal)

A tela principal é o seu ponto de partida para criar e gerenciar mapas.

*   **Criar com IA:** Digite um tópico no campo de texto e clique em **"Gerar Mapa"**. A IA criará um novo mapa mental estruturado sobre o tema.
*   **Criar em Branco:** Clique em **"Criar Mapa em Branco"** para iniciar um mapa mental do zero no editor.
*   **Biblioteca de Mapas:** Todos os seus mapas salvos são listados aqui. Você pode:
    *   **Pesquisar:** Use a barra de busca para encontrar mapas pelo título.
    *   **Editar:** Clique em **"Editar"** para abrir o mapa no editor.
    *   **Excluir:** Clique no ícone de lixeira para remover um mapa permanentemente.

### Editor de Mapas Mentais

O editor é onde você visualiza e modifica seus mapas mentais.

*   **Canvas Interativo:** Arraste os nós para reposicioná-los. Use o scroll do mouse para dar zoom e clique e arraste no fundo para mover a visualização (pan).
*   **Chat para Expansão:** Use a interface de chat para fazer perguntas e adicionar novas ideias ao mapa mental existente.
*   **Painel de Controle (Superior):**
    *   **Salvar:** Salva o estado atual do mapa.
    *   **Exportar:** Gera um arquivo Markdown (.md) ou PDF do seu mapa.
    *   **Voltar:** Retorna à tela principal.

### Gerenciamento de Nós e Conexões

*   **Editar Texto:** Clique duas vezes em um nó para editar seu conteúdo.
*   **Adicionar Conexão:** Arraste de um ponto de conexão (círculo na borda de um nó) para outro nó para criar um link manual.
*   **Remover Nó/Conexão:** Selecione o elemento e pressione a tecla `Delete`.

---

## 4. Configurações

Acesse a janela de configurações clicando no ícone de engrenagem (⚙️).

### Provedor de IA

Você pode escolher entre dois tipos de provedores:

1.  **OpenRouter (Recomendado):**
    *   **O que é:** Um serviço online que dá acesso a dezenas de modelos de IA (GPT, Claude, Llama, etc.).
    *   **Como configurar:**
        1.  Crie uma conta em [openrouter.ai](https://openrouter.ai).
        2.  Obtenha sua chave de API ("API Key").
        3.  Cole a chave no campo **OpenRouter API Key** nas configurações e salve.

2.  **Ollama (Local):**
    *   **O que é:** Uma ferramenta para rodar modelos de IA diretamente no seu computador, garantindo privacidade e uso offline.
    *   **Como configurar:**
        1.  Instale o Ollama de [ollama.com](https://ollama.com).
        2.  Execute um modelo (ex: `ollama run llama3`).
        3.  Mantenha a URL padrão (`http://localhost:11434`) nas configurações.

### Teste de Conexão

Após configurar um provedor, use o botão **"Testar Conexão"** para garantir que a aplicação consegue se comunicar com o serviço de IA.

---

## 5. Exemplos de Prompts para IA

Use estes exemplos como inspiração para gerar seus mapas mentais.

### Brainstorming e Criatividade

*   `Crie um mapa mental sobre ideias para um canal no YouTube sobre culinária vegana.`
*   `Quais são as possíveis estratégias de marketing para uma startup de moda sustentável?`
*   `Brainstorm de nomes para uma nova marca de café especial.`

### Aprendizado e Estudo

*   `Explique os conceitos fundamentais da física quântica em um mapa mental.`
*   `Faça um mapa mental resumindo o livro "Hábitos Atômicos".`
*   `Estruture um plano de estudos para aprender a programar em Python em 3 meses.`

### Planejamento de Projetos

*   `Crie um mapa mental para organizar o lançamento de um novo aplicativo, incluindo fases de pré-lançamento, lançamento e pós-lançamento.`
*   `Estruture as etapas para organizar uma conferência de tecnologia.`
*   `Mapa mental para planejar uma viagem de 15 dias para o Japão.`

### Negócios e Estratégia

*   `Análise SWOT completa para a empresa Coca-Cola.`
*   `Gere um mapa mental detalhando um plano de negócios para uma cafeteria local.`
*   `Quais são os pilares de uma cultura de inovação em uma empresa de tecnologia?`

---
Este guia será atualizado conforme novas funcionalidades forem adicionadas.
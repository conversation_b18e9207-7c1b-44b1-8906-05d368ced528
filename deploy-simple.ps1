# Deploy Simples do Mindscape
# Este script faz o deploy usando comandos básicos

Write-Host "🚀 Iniciando deploy do Mindscape..." -ForegroundColor Blue

$SERVER = "**************"
$USER = "usuario"
$PASSWORD = "Senh@01020304"

Write-Host "📋 Instruções para Deploy Manual:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. 📤 UPLOAD DO ARQUIVO:" -ForegroundColor Green
Write-Host "   - Baixe e instale WinSCP: https://winscp.net/" -ForegroundColor White
Write-Host "   - Conecte em: $SERVER" -ForegroundColor White
Write-Host "   - Usuário: $USER" -ForegroundColor White
Write-Host "   - Senha: $PASSWORD" -ForegroundColor White
Write-Host "   - Navegue até: /home/<USER>/" -ForegroundColor White
Write-Host "   - Crie pasta: mindscape-app" -ForegroundColor White
Write-Host "   - Faça upload do arquivo: mindscape-deploy.tar.gz" -ForegroundColor White
Write-Host ""

Write-Host "2. 🔧 CONECTAR AO SERVIDOR:" -ForegroundColor Green
Write-Host "   - Baixe e instale PuTTY: https://www.putty.org/" -ForegroundColor White
Write-Host "   - Conecte em: $SERVER" -ForegroundColor White
Write-Host "   - Usuário: $USER" -ForegroundColor White
Write-Host "   - Senha: $PASSWORD" -ForegroundColor White
Write-Host ""

Write-Host "3. ⚙️ COMANDOS PARA EXECUTAR NO SERVIDOR:" -ForegroundColor Green
Write-Host ""

$commands = @"
# Ir para o diretório
cd /home/<USER>/mindscape-app

# Atualizar sistema
sudo apt-get update -y

# Instalar Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar versão
node --version
npm --version

# Instalar PM2
sudo npm install -g pm2

# Extrair aplicação
tar -xzf mindscape-deploy.tar.gz
rm mindscape-deploy.tar.gz

# Instalar dependências
npm install --production

# Fazer build
npm run build

# Criar diretório de logs
mkdir -p logs

# Parar aplicação existente (se houver)
pm2 stop mindscape 2>/dev/null || true
pm2 delete mindscape 2>/dev/null || true

# Iniciar aplicação na porta 3030
pm2 start npm --name "mindscape" -- start

# Salvar configuração
pm2 save

# Configurar auto-start
sudo env PATH=`$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u usuario --hp /home/<USER>

# Verificar status
pm2 status

# Ver logs
pm2 logs mindscape --lines 20

# Testar aplicação
curl http://localhost:3030

# Configurar firewall
sudo ufw allow 3030
sudo ufw status
"@

Write-Host $commands -ForegroundColor Cyan
Write-Host ""

Write-Host "4. 🌐 VERIFICAR APLICAÇÃO:" -ForegroundColor Green
Write-Host "   Após executar os comandos, acesse:" -ForegroundColor White
Write-Host "   http://$SERVER`:3030" -ForegroundColor Yellow
Write-Host ""

Write-Host "5. 📊 COMANDOS DE MONITORAMENTO:" -ForegroundColor Green
Write-Host "   pm2 status          # Ver status" -ForegroundColor White
Write-Host "   pm2 logs mindscape  # Ver logs" -ForegroundColor White
Write-Host "   pm2 restart mindscape # Reiniciar" -ForegroundColor White
Write-Host "   pm2 monit           # Monitor recursos" -ForegroundColor White
Write-Host ""

# Verificar se o arquivo de deploy existe
if (Test-Path "mindscape-deploy.tar.gz") {
    $fileSize = (Get-Item "mindscape-deploy.tar.gz").Length
    Write-Host "✅ Arquivo de deploy pronto: mindscape-deploy.tar.gz ($([math]::Round($fileSize/1KB, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "❌ Arquivo mindscape-deploy.tar.gz não encontrado!" -ForegroundColor Red
    Write-Host "Execute primeiro: tar --exclude='node_modules' --exclude='.git' --exclude='.next' --exclude='logs' --exclude='*.log' --exclude='mindscape-deploy.tar.gz' -czf mindscape-deploy.tar.gz ." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔧 TROUBLESHOOTING:" -ForegroundColor Red
Write-Host "Se a aplicação não funcionar:" -ForegroundColor White
Write-Host "1. Verifique logs: pm2 logs mindscape" -ForegroundColor White
Write-Host "2. Verifique porta: sudo lsof -i :3030" -ForegroundColor White
Write-Host "3. Reinicie: pm2 restart mindscape" -ForegroundColor White
Write-Host "4. Teste local: curl http://localhost:3030" -ForegroundColor White
Write-Host ""

Write-Host "📞 SUPORTE:" -ForegroundColor Magenta
Write-Host "Se precisar de ajuda, verifique:" -ForegroundColor White
Write-Host "- deploy-manual.md (guia detalhado)" -ForegroundColor White
Write-Host "- DEPLOY.md (documentação técnica)" -ForegroundColor White
Write-Host ""

Write-Host "🎉 Siga as instruções acima para fazer o deploy!" -ForegroundColor Green

# Tentar abrir WinSCP se estiver instalado
$winscpPath = "C:\Program Files (x86)\WinSCP\WinSCP.exe"
if (Test-Path $winscpPath) {
    Write-Host ""
    Write-Host "🔧 WinSCP encontrado! Deseja abrir? (s/n): " -ForegroundColor Yellow -NoNewline
    $response = Read-Host
    if ($response -eq "s" -or $response -eq "S") {
        Start-Process $winscpPath
        Write-Host "✅ WinSCP aberto! Configure a conexão com as credenciais acima." -ForegroundColor Green
    }
}

# Tentar abrir PuTTY se estiver instalado
$puttyPath = "C:\Program Files\PuTTY\putty.exe"
if (Test-Path $puttyPath) {
    Write-Host ""
    Write-Host "🔧 PuTTY encontrado! Deseja abrir? (s/n): " -ForegroundColor Yellow -NoNewline
    $response = Read-Host
    if ($response -eq "s" -or $response -eq "S") {
        Start-Process $puttyPath -ArgumentList "-ssh", "$USER@$SERVER"
        Write-Host "✅ PuTTY aberto! Use a senha: $PASSWORD" -ForegroundColor Green
    }
}

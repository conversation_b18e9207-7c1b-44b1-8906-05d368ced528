import { type NextRequest, NextResponse } from "next/server"
import { deleteMindMap, getMindMapById, saveMindMap } from "@/lib/database"
import { getUserFromRequest } from "@/lib/middleware"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const mindMapFromDb = await getMindMapById(id)

    if (!mindMapFromDb) {
      return NextResponse.json({ error: "Mind map not found" }, { status: 404 })
    }

    // Check if the mind map is protected by password
    if ((mindMapFromDb as any).is_protected) {
      const mindMap = {
        id: mindMapFromDb.id,
        titulo: mindMapFromDb.name,
        criado_em: new Date(mindMapFromDb.created_at).toISOString(),
        atualizado_em: new Date(mindMapFromDb.updated_at).toISOString(),
        descricao: mindMapFromDb.description,
        user_id: mindMapFromDb.user_id,
        is_protected: true,
        // Don't return the actual data for protected maps
        dados: { nodes: [], connections: [] }
      }

      return NextResponse.json({
        mindMap,
        isProtected: true,
        message: "Este mapa mental está protegido por senha"
      })
    }

    const mindMap = {
      id: mindMapFromDb.id,
      titulo: mindMapFromDb.name,
      dados: mindMapFromDb.data,
      criado_em: new Date(mindMapFromDb.created_at).toISOString(),
      atualizado_em: new Date(mindMapFromDb.updated_at).toISOString(),
      descricao: mindMapFromDb.description,
      user_id: mindMapFromDb.user_id,
      is_protected: (mindMapFromDb as any).is_protected || false
    }

    return NextResponse.json({ mindMap })
  } catch (error) {
    console.error("Error getting mind map:", error)
    return NextResponse.json({ error: "Failed to get mind map" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const body = await request.json()
    const { titulo, descricao, dados } = body

    if (!titulo || !dados) {
      return NextResponse.json({ error: "Título e dados são obrigatórios" }, { status: 400 })
    }

    // Get user from request (optional for backward compatibility)
    const user = getUserFromRequest(request)
    const userId = user?.userId

    // Update the mind map
    await saveMindMap({
      id,
      name: titulo,
      description: descricao,
      data: dados,
      user_id: userId,
    })

    // Return the updated mind map
    const mindMap = {
      id: id,
      titulo: titulo,
      dados: dados,
      descricao: descricao,
      user_id: userId,
      atualizado_em: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      mindMap: mindMap
    })
  } catch (error) {
    console.error("Error updating mind map:", error)
    return NextResponse.json({ error: "Failed to update mind map" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    await deleteMindMap(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting mind map:", error)
    return NextResponse.json({ error: "Failed to delete mind map" }, { status: 500 })
  }
}

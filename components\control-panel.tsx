"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { PasswordModal } from "@/components/password-modal"
import { ZoomIn, ZoomOut, Save, FolderOpen, Trash2, Info, Plus, Lock, Shield } from "lucide-react"
import type { MindMapData } from "@/types/mindmap"

interface ControlPanelProps {
  zoom: number
  onZoomChange: (zoom: number) => void
  onSave: (password?: string) => void
  onLoad: (mapData: MindMapData, password?: string) => void
  onClear: () => void
  onNew: () => void
  nodeCount: number
  connectionCount: number
  hasUnsavedChanges: boolean
  theme: "claro" | "escuro"
}

export function ControlPanel({
  zoom,
  onZoom<PERSON>hange,
  onSave,
  onLoad,
  onClear,
  onNew,
  nodeCount,
  connectionCount,
  hasUnsavedChanges,
  theme,
}: ControlPanelProps) {
  const [savedMaps, setSavedMaps] = useState<MindMapData[]>([])
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [isCreatingPassword, setIsCreatingPassword] = useState(false)

  const isDark = theme === "escuro"

  const loadSavedMaps = async () => {
    try {
      const response = await fetch("/api/mindmaps")
      if (response.ok) {
        const maps = await response.json()
        setSavedMaps(maps)
        setIsLoadDialogOpen(true)
      }
    } catch (error) {
      console.error("Erro ao carregar mapas:", error)
    }
  }

  const handleLoad = (mapData: MindMapData) => {
    onLoad(mapData)
    setIsLoadDialogOpen(false)
  }

  const deleteSavedMap = async (id: string) => {
    if (confirm("Tem certeza que deseja deletar este mapa mental?")) {
      try {
        const response = await fetch(`/api/mindmaps/${id}`, { method: "DELETE" })
        if (response.ok) {
          setSavedMaps((prev) => prev.filter((map) => map.id !== id))
        }
      } catch (error) {
        console.error("Erro ao deletar mapa:", error)
      }
    }
  }

  const handleSaveWithPassword = () => {
    setIsCreatingPassword(true)
    setShowPasswordModal(true)
  }

  const handlePasswordSubmit = (password: string) => {
    onSave(password)
    setShowPasswordModal(false)
    setIsCreatingPassword(false)
  }

  return (
    <>
      <PasswordModal
        isOpen={showPasswordModal}
        onClose={() => {
          setShowPasswordModal(false)
          setIsCreatingPassword(false)
        }}
        onSubmit={handlePasswordSubmit}
        mapName="Novo Mapa Mental"
        isCreating={isCreatingPassword}
      />

      <div className={`border-b p-4 ${isDark ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"}`}>
        <div className="flex items-center justify-between">
          {/* Lado esquerdo - Controles de zoom */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => onZoomChange(Math.max(0.1, zoom - 0.1))}>
                <ZoomOut className="w-4 h-4" />
              </Button>

              <div className="w-32">
                <Slider
                  value={[zoom]}
                  onValueChange={([value]) => onZoomChange(value)}
                  min={0.1}
                  max={3}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <Button variant="outline" size="sm" onClick={() => onZoomChange(Math.min(3, zoom + 0.1))}>
                <ZoomIn className="w-4 h-4" />
              </Button>

              <span className={`text-sm min-w-[60px] ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                {Math.round(zoom * 100)}%
              </span>
            </div>
          </div>

          {/* Centro - Estatísticas */}
          <div className={`flex items-center gap-4 text-sm ${isDark ? "text-gray-300" : "text-gray-600"}`}>
            <div className="flex items-center gap-1">
              <Info className="w-4 h-4" />
              <span>{nodeCount} nós</span>
            </div>
            <div className={isDark ? "text-gray-600" : "text-gray-300"}>•</div>
            <div>{connectionCount} conexões</div>
            {hasUnsavedChanges && (
              <>
                <div className={isDark ? "text-gray-600" : "text-gray-300"}>•</div>
                <div className="text-yellow-600">Não salvo</div>
              </>
            )}
          </div>

          {/* Lado direito - Botões de ação */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={onNew}>
              <Plus className="w-4 h-4 mr-2" />
              Novo
            </Button>

            <div className="flex">
              <Button variant="outline" size="sm" onClick={() => onSave()} className="rounded-r-none">
                <Save className="w-4 h-4 mr-2" />
                Salvar
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveWithPassword}
                className="rounded-l-none border-l-0 px-2 bg-transparent"
                title="Salvar com senha"
              >
                <Lock className="w-4 h-4" />
              </Button>
            </div>

            <Dialog open={isLoadDialogOpen} onOpenChange={setIsLoadDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" onClick={loadSavedMaps}>
                  <FolderOpen className="w-4 h-4 mr-2" />
                  Carregar
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Carregar Mapa Mental</DialogTitle>
                </DialogHeader>
                <ScrollArea className="max-h-96">
                  <div className="space-y-2">
                    {savedMaps.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">Nenhum mapa mental salvo encontrado</p>
                    ) : (
                      savedMaps.map((map) => (
                        <div
                          key={map.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{map.name}</h4>
                              {map.protectedByPassword && <Shield className="w-4 h-4 text-yellow-600" />}
                            </div>
                            <p className="text-sm text-gray-500">
                              {map.nodes.length} nós • {new Date(map.createdAt).toLocaleDateString("pt-BR")}
                            </p>
                            {map.description && <p className="text-xs text-gray-400 mt-1">{map.description}</p>}
                          </div>
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm" onClick={() => handleLoad(map)}>
                              Carregar
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => deleteSavedMap(map.id)}>
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </DialogContent>
            </Dialog>

            <Button variant="outline" size="sm" onClick={onClear}>
              <Trash2 className="w-4 h-4 mr-2" />
              Limpar
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

export type Language = "pt" | "en"

export interface Translations {
  // Setup Wizard
  setupWizard: {
    title: string
    subtitle: string
    step: string
    language: {
      title: string
      description: string
      portuguese: string
      english: string
    }
    database: {
      title: string
      description: string
      local: string
      localDescription: string
      neon: string
      neonDescription: string
      connectionString: string
      connectionStringPlaceholder: string
      testConnection: string
      testing: string
      connectionSuccess: string
      connectionError: string
    }
    ai: {
      title: string
      description: string
      provider: string
      openrouter: string
      openrouterDescription: string
      ollama: string
      ollamaDescription: string
      apiKey: string
      apiKeyPlaceholder: string
      baseUrl: string
      baseUrlPlaceholder: string
      defaultModel: string
      testConnection: string
      testing: string
      connectionSuccess: string
      connectionError: string
    }
    complete: {
      title: string
      description: string
      summary: string
      database: string
      aiProvider: string
      defaultModel: string
      startUsing: string
    }
    buttons: {
      next: string
      previous: string
      finish: string
      skip: string
    }
  }
  // Main App
  mainApp: {
    title: string
    settings: string
    createNew: string
    createDescription: string
    generate: string
    generating: string
    yourMindMaps: string
    mindMapsCount: string
    searchPlaceholder: string
    noMindMaps: string
    noMindMapsDescription: string
    noMatching: string
    noMatchingDescription: string
    nodes: string
    view: string
    delete: string
    confirmDelete: string
    topicPlaceholder: string
    topicRequired: string
    generateSuccess: string
    generateError: string
    deleteSuccess: string
    deleteError: string
    loadError: string
  }
  // Config Modal
  config: {
    title: string
    language: string
    database: string
    aiProvider: string
    apiKey: string
    baseUrl: string
    defaultModel: string
    testConnection: string
    testing: string
    save: string
    cancel: string
    saveSuccess: string
    saveError: string
    connectionSuccess: string
    connectionError: string
  }
  // Mind Map Viewer
  viewer: {
    backToList: string
    export: string
    exportPng: string
    exportJson: string
    zoom: string
    zoomIn: string
    zoomOut: string
    resetZoom: string
    fitToScreen: string
    createdAt: string
    updatedAt: string
    nodes: string
    connections: string
  }
  // Common
  common: {
    loading: string
    error: string
    success: string
    cancel: string
    save: string
    delete: string
    edit: string
    close: string
    back: string
    next: string
    previous: string
    finish: string
    yes: string
    no: string
  }
}

const translations: Record<Language, Translations> = {
  pt: {
    setupWizard: {
      title: "Assistente de Configuração",
      subtitle: "Configure sua aplicação Mindscape em poucos passos",
      step: "Passo",
      language: {
        title: "Escolha seu Idioma",
        description: "Selecione o idioma da interface",
        portuguese: "Português (Brasil)",
        english: "English (US)",
      },
      database: {
        title: "Configuração do Banco de Dados",
        description: "Escolha onde armazenar seus mapas mentais",
        local: "SQLite Local",
        localDescription: "Armazenamento local no dispositivo",
        neon: "Neon Database",
        neonDescription: "Banco PostgreSQL na nuvem",
        connectionString: "String de Conexão",
        connectionStringPlaceholder: "postgresql://user:password@host:port/database",
        testConnection: "Testar Conexão",
        testing: "Testando...",
        connectionSuccess: "Conexão bem-sucedida!",
        connectionError: "Erro na conexão",
      },
      ai: {
        title: "Configuração da IA",
        description: "Configure seu provedor de inteligência artificial",
        provider: "Provedor",
        openrouter: "OpenRouter",
        openrouterDescription: "Acesso a múltiplos modelos de IA",
        ollama: "Ollama",
        ollamaDescription: "IA local em seu dispositivo",
        apiKey: "Chave da API",
        apiKeyPlaceholder: "Insira sua chave da API",
        baseUrl: "URL Base",
        baseUrlPlaceholder: "http://localhost:11434",
        defaultModel: "Modelo Padrão",
        testConnection: "Testar Conexão",
        testing: "Testando...",
        connectionSuccess: "Conexão com IA bem-sucedida!",
        connectionError: "Erro na conexão com IA",
      },
      complete: {
        title: "Configuração Concluída!",
        description: "Sua aplicação está pronta para uso",
        summary: "Resumo da Configuração:",
        database: "Banco de Dados:",
        aiProvider: "Provedor de IA:",
        defaultModel: "Modelo Padrão:",
        startUsing: "Começar a Usar",
      },
      buttons: {
        next: "Próximo",
        previous: "Anterior",
        finish: "Finalizar",
        skip: "Pular",
      },
    },
    mainApp: {
      title: "Mindscape",
      settings: "Configurações",
      createNew: "Criar Novo Mapa Mental",
      createDescription: "Digite um tópico e deixe a IA gerar um mapa mental abrangente para você",
      generate: "Gerar",
      generating: "Gerando...",
      yourMindMaps: "Seus Mapas Mentais",
      mindMapsCount: "mapas mentais criados",
      searchPlaceholder: "Buscar mapas mentais...",
      noMindMaps: "Nenhum mapa mental ainda",
      noMindMapsDescription: "Crie seu primeiro mapa mental com IA usando o gerador acima",
      noMatching: "Nenhum mapa mental encontrado",
      noMatchingDescription: "Tente ajustar seus termos de busca",
      nodes: "nós",
      view: "Visualizar",
      delete: "Excluir",
      confirmDelete: "Tem certeza que deseja excluir este mapa mental?",
      topicPlaceholder: 'Digite seu tópico (ex: "Gestão de Projetos", "Aprender JavaScript", "Vida Saudável")',
      topicRequired: "Por favor, digite um tópico para seu mapa mental",
      generateSuccess: "Mapa mental gerado e salvo com sucesso!",
      generateError: "Falha ao gerar mapa mental",
      deleteSuccess: "Mapa mental excluído com sucesso",
      deleteError: "Falha ao excluir mapa mental",
      loadError: "Falha ao carregar mapas mentais",
    },
    config: {
      title: "Configurações",
      language: "Idioma",
      database: "Banco de Dados",
      aiProvider: "Provedor de IA",
      apiKey: "Chave da API",
      baseUrl: "URL Base",
      defaultModel: "Modelo Padrão",
      testConnection: "Testar Conexão",
      testing: "Testando...",
      save: "Salvar",
      cancel: "Cancelar",
      saveSuccess: "Configurações salvas com sucesso!",
      saveError: "Erro ao salvar configurações",
      connectionSuccess: "Conexão bem-sucedida!",
      connectionError: "Erro na conexão",
    },
    viewer: {
      backToList: "Voltar à Lista",
      export: "Exportar",
      exportPng: "Exportar como PNG",
      exportJson: "Exportar como JSON",
      zoom: "Zoom",
      zoomIn: "Aumentar Zoom",
      zoomOut: "Diminuir Zoom",
      resetZoom: "Resetar Zoom",
      fitToScreen: "Ajustar à Tela",
      createdAt: "Criado em",
      updatedAt: "Atualizado em",
      nodes: "Nós",
      connections: "Conexões",
    },
    common: {
      loading: "Carregando...",
      error: "Erro",
      success: "Sucesso",
      cancel: "Cancelar",
      save: "Salvar",
      delete: "Excluir",
      edit: "Editar",
      close: "Fechar",
      back: "Voltar",
      next: "Próximo",
      previous: "Anterior",
      finish: "Finalizar",
      yes: "Sim",
      no: "Não",
    },
  },
  en: {
    setupWizard: {
      title: "Setup Wizard",
      subtitle: "Configure your Mind Map AI application in a few steps",
      step: "Step",
      language: {
        title: "Choose your Language",
        description: "Select the interface language",
        portuguese: "Português (Brasil)",
        english: "English (US)",
      },
      database: {
        title: "Database Configuration",
        description: "Choose where to store your mind maps",
        local: "Local SQLite",
        localDescription: "Local storage on device",
        neon: "Neon Database",
        neonDescription: "PostgreSQL cloud database",
        connectionString: "Connection String",
        connectionStringPlaceholder: "postgresql://user:password@host:port/database",
        testConnection: "Test Connection",
        testing: "Testing...",
        connectionSuccess: "Connection successful!",
        connectionError: "Connection error",
      },
      ai: {
        title: "AI Configuration",
        description: "Configure your artificial intelligence provider",
        provider: "Provider",
        openrouter: "OpenRouter",
        openrouterDescription: "Access to multiple AI models",
        ollama: "Ollama",
        ollamaDescription: "Local AI on your device",
        apiKey: "API Key",
        apiKeyPlaceholder: "Enter your API key",
        baseUrl: "Base URL",
        baseUrlPlaceholder: "http://localhost:11434",
        defaultModel: "Default Model",
        testConnection: "Test Connection",
        testing: "Testing...",
        connectionSuccess: "AI connection successful!",
        connectionError: "AI connection error",
      },
      complete: {
        title: "Setup Complete!",
        description: "Your application is ready to use",
        summary: "Configuration Summary:",
        database: "Database:",
        aiProvider: "AI Provider:",
        defaultModel: "Default Model:",
        startUsing: "Start Using",
      },
      buttons: {
        next: "Next",
        previous: "Previous",
        finish: "Finish",
        skip: "Skip",
      },
    },
    mainApp: {
      title: "Mind Map AI",
      settings: "Settings",
      createNew: "Create New Mind Map",
      createDescription: "Enter a topic and let AI generate a comprehensive mind map for you",
      generate: "Generate",
      generating: "Generating...",
      yourMindMaps: "Your Mind Maps",
      mindMapsCount: "mind maps created",
      searchPlaceholder: "Search mind maps...",
      noMindMaps: "No mind maps yet",
      noMindMapsDescription: "Create your first AI-powered mind map using the generator above",
      noMatching: "No matching mind maps",
      noMatchingDescription: "Try adjusting your search terms",
      nodes: "nodes",
      view: "View",
      delete: "Delete",
      confirmDelete: "Are you sure you want to delete this mind map?",
      topicPlaceholder: 'Enter your topic (e.g., "Project Management", "Learning JavaScript", "Healthy Lifestyle")',
      topicRequired: "Please enter a topic for your mind map",
      generateSuccess: "Mind map generated and saved successfully!",
      generateError: "Failed to generate mind map",
      deleteSuccess: "Mind map deleted successfully",
      deleteError: "Failed to delete mind map",
      loadError: "Failed to load mind maps",
    },
    config: {
      title: "Settings",
      language: "Language",
      database: "Database",
      aiProvider: "AI Provider",
      apiKey: "API Key",
      baseUrl: "Base URL",
      defaultModel: "Default Model",
      testConnection: "Test Connection",
      testing: "Testing...",
      save: "Save",
      cancel: "Cancel",
      saveSuccess: "Settings saved successfully!",
      saveError: "Error saving settings",
      connectionSuccess: "Connection successful!",
      connectionError: "Connection error",
    },
    viewer: {
      backToList: "Back to List",
      export: "Export",
      exportPng: "Export as PNG",
      exportJson: "Export as JSON",
      zoom: "Zoom",
      zoomIn: "Zoom In",
      zoomOut: "Zoom Out",
      resetZoom: "Reset Zoom",
      fitToScreen: "Fit to Screen",
      createdAt: "Created at",
      updatedAt: "Updated at",
      nodes: "Nodes",
      connections: "Connections",
    },
    common: {
      loading: "Loading...",
      error: "Error",
      success: "Success",
      cancel: "Cancel",
      save: "Save",
      delete: "Delete",
      edit: "Edit",
      close: "Close",
      back: "Back",
      next: "Next",
      previous: "Previous",
      finish: "Finish",
      yes: "Yes",
      no: "No",
    },
  },
}

let currentLanguage: Language = "pt" // Default to Portuguese

export function setLanguage(lang: Language) {
  currentLanguage = lang
  if (typeof window !== "undefined") {
    localStorage.setItem("language", lang)
  }
}

export function getLanguage(): Language {
  if (typeof window !== "undefined") {
    const stored = localStorage.getItem("language") as Language
    if (stored && (stored === "pt" || stored === "en")) {
      currentLanguage = stored
      return stored
    }
    // Force Portuguese as default
    localStorage.setItem("language", "pt")
  }
  return "pt"
}

export function useTranslation() {
  const language = getLanguage()
  return {
    t: translations[language],
    language,
    setLanguage,
  }
}

// Initialize with Portuguese
if (typeof window !== "undefined") {
  const stored = localStorage.getItem("language")
  if (!stored || (stored !== "pt" && stored !== "en")) {
    localStorage.setItem("language", "pt")
  }
  currentLanguage = "pt"
}

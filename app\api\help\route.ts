import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function GET() {
  try {
    // O caminho para o HELP.md na raiz do projeto
    const filePath = path.join(process.cwd(), 'HELP.md');
    const markdownContent = await fs.readFile(filePath, 'utf-8');
    
    return NextResponse.json({ content: markdownContent });
  } catch (error) {
    console.error('Failed to read HELP.md:', error);
    return NextResponse.json(
      { error: 'Could not load help content.' },
      { status: 500 }
    );
  }
}
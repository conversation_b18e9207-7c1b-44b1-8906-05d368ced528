import { db, getDatabase } from "./database"
import { configuracoes } from "./schema"
import { env } from "./env"

export const config = {
  app: {
    name: "Mindscape",
    version: "1.0.0",
    environment: env.NODE_ENV,
    url: env.VERCEL_URL ? `https://${env.VERCEL_URL}` : "http://localhost:3000",
  },
  database: {
    url: env.NEON_DATABASE_URL,
    maxConnections: env.MAX_CONNECTIONS,
    connectionTimeout: env.CONNECTION_TIMEOUT,
    queryTimeout: env.QUERY_TIMEOUT,
  },
  security: {
    sessionSecret: env.SESSION_SECRET,
    encryptionKey: env.ENCRYPTION_KEY,
    bcryptRounds: 12,
    rateLimitEnabled: env.RATE_LIMIT_ENABLED,
  },
  ai: {
    openrouterApiKey: env.OPENROUTER_API_KEY,
    ollamaBaseUrl: env.OLLAMA_BASE_URL,
  },
  features: {
    metricsEnabled: env.METRICS_ENABLED,
    backupEnabled: env.BACKUP_ENABLED,
  },
  logging: {
    level: env.LOG_LEVEL,
  },
}

import { type ConfiguracaoSelect } from "./schema"

export async function loadConfig() {
  const db = await getDatabase()
  try {
    const stmt = db.prepare("SELECT chave, valor FROM configuracoes")
    const rows = stmt.all() as { chave: string; valor: string }[]

    const configObject = rows.reduce(
      (acc: Record<string, any>, { chave, valor }) => {
        // Convert "true" and "false" strings to booleans
        if (valor === "true") {
          acc[chave] = true
        } else if (valor === "false") {
          acc[chave] = false
        } else {
          acc[chave] = valor
        }
        return acc
      },
      {},
    )
    return configObject
  } catch (error) {
    console.error("Erro ao carregar configurações:", error)
    return {}
  }
}

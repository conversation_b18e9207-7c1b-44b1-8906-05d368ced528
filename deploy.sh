#!/bin/bash

# Deploy script for Mindscape application
# Server: **************
# User: usuario
# Port: 3030

echo "🚀 Starting Mindscape deployment..."

# Server configuration
SERVER_HOST="**************"
SERVER_USER="usuario"
SERVER_PASSWORD="Senh@01020304"
APP_DIR="/home/<USER>/mindscape-app"
APP_PORT="3030"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📦 Preparing application for deployment...${NC}"

# Create deployment package (excluding node_modules and .git)
echo -e "${YELLOW}Creating deployment package...${NC}"
tar --exclude='node_modules' \
    --exclude='.git' \
    --exclude='.next' \
    --exclude='deploy.sh' \
    --exclude='*.log' \
    -czf mindscape-deploy.tar.gz .

echo -e "${GREEN}✅ Deployment package created: mindscape-deploy.tar.gz${NC}"

# Upload and deploy using sshpass
echo -e "${BLUE}🔄 Uploading to server...${NC}"

# Install sshpass if not available (for automated SSH)
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}Installing sshpass for automated deployment...${NC}"
    # For Ubuntu/Debian
    sudo apt-get update && sudo apt-get install -y sshpass 2>/dev/null || \
    # For CentOS/RHEL
    sudo yum install -y sshpass 2>/dev/null || \
    # For macOS
    brew install hudochenkov/sshpass/sshpass 2>/dev/null || \
    echo -e "${RED}❌ Please install sshpass manually${NC}"
fi

# Deploy to server
sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'ENDSSH'
    echo "🔧 Setting up server environment..."
    
    # Update system
    sudo apt-get update
    
    # Install Node.js 18+ if not installed
    if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 18 ]]; then
        echo "📦 Installing Node.js 18..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # Install PM2 for process management
    if ! command -v pm2 &> /dev/null; then
        echo "📦 Installing PM2..."
        sudo npm install -g pm2
    fi
    
    # Create application directory
    mkdir -p /home/<USER>/mindscape-app
    cd /home/<USER>/mindscape-app
    
    echo "✅ Server environment ready"
ENDSSH

# Upload the application
echo -e "${YELLOW}📤 Uploading application files...${NC}"
sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no mindscape-deploy.tar.gz "$SERVER_USER@$SERVER_HOST:$APP_DIR/"

# Extract and setup application
sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << ENDSSH
    cd $APP_DIR
    
    echo "📦 Extracting application..."
    tar -xzf mindscape-deploy.tar.gz
    rm mindscape-deploy.tar.gz
    
    echo "📦 Installing dependencies..."
    npm install --production
    
    echo "🔧 Building application..."
    npm run build
    
    echo "⚙️ Creating PM2 ecosystem file..."
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscape',
    script: 'npm',
    args: 'start',
    cwd: '$APP_DIR',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: '$APP_PORT'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF
    
    # Create logs directory
    mkdir -p logs
    
    # Stop existing application if running
    pm2 stop mindscape 2>/dev/null || true
    pm2 delete mindscape 2>/dev/null || true
    
    echo "🚀 Starting application on port $APP_PORT..."
    pm2 start ecosystem.config.js
    
    # Save PM2 configuration
    pm2 save
    
    # Setup PM2 to start on boot
    sudo env PATH=\$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u usuario --hp /home/<USER>
    
    echo "✅ Application deployed successfully!"
    echo "🌐 Access your application at: http://$SERVER_HOST:$APP_PORT"
    
    # Show application status
    pm2 status
    pm2 logs mindscape --lines 10
ENDSSH

# Cleanup local deployment package
rm -f mindscape-deploy.tar.gz

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${BLUE}📱 Your Mindscape application is now running at:${NC}"
echo -e "${GREEN}   http://$SERVER_HOST:$APP_PORT${NC}"
echo -e "${YELLOW}📊 To monitor the application:${NC}"
echo -e "   ssh $SERVER_USER@$SERVER_HOST"
echo -e "   pm2 status"
echo -e "   pm2 logs mindscape"
echo -e "${YELLOW}🔄 To restart the application:${NC}"
echo -e "   ssh $SERVER_USER@$SERVER_HOST"
echo -e "   pm2 restart mindscape"

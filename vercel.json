{"functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NODE_ENV": "production", "VERCEL": "1"}, "build": {"env": {"NODE_ENV": "production"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}
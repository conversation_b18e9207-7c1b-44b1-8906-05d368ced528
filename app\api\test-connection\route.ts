import { type NextRequest, NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url } = body

    if (type === "database") {
      if (database_type === "neon") {
        if (!database_url) {
          return NextResponse.json({ success: false, error: "URL do banco de dados é obrigatória" }, { status: 400 })
        }

        try {
          const sql = neon(database_url)
          await sql`SELECT 1`
          return NextResponse.json({ success: true, message: "Conexão com Neon bem-sucedida" })
        } catch (error) {
          return NextResponse.json({ success: false, error: `Falha na conexão com Neon: ${error}` }, { status: 400 })
        }
      } else {
        // SQLite is always available locally
        return NextResponse.json({ success: true, message: "SQLite local disponível" })
      }
    }

    if (type === "ai") {
      if (llm_provider === "openrouter") {
        if (!openrouter_api_key) {
          return NextResponse.json({ success: false, error: "Chave da API OpenRouter é obrigatória" }, { status: 400 })
        }

        try {
          const response = await fetch("https://openrouter.ai/api/v1/models", {
            headers: {
              Authorization: `Bearer ${openrouter_api_key}`,
              "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
              "X-Title": "Mind Map AI",
            },
          })

          if (response.ok) {
            return NextResponse.json({ success: true, message: "Conexão com OpenRouter bem-sucedida" })
          } else {
            throw new Error(`HTTP ${response.status}`)
          }
        } catch (error) {
          return NextResponse.json(
            { success: false, error: `Falha na conexão com OpenRouter: ${error}` },
            { status: 400 },
          )
        }
      } else if (llm_provider === "ollama") {
        const baseUrl = ollama_base_url || "http://localhost:11434"

        try {
          const response = await fetch(`${baseUrl}/api/tags`)
          if (response.ok) {
            return NextResponse.json({ success: true, message: "Conexão com Ollama bem-sucedida" })
          } else {
            throw new Error(`HTTP ${response.status}`)
          }
        } catch (error) {
          return NextResponse.json({ success: false, error: `Falha na conexão com Ollama: ${error}` }, { status: 400 })
        }
      }
    }

    return NextResponse.json({ success: false, error: "Tipo de teste inválido" }, { status: 400 })
  } catch (error) {
    console.error("Erro no teste de conexão:", error)
    return NextResponse.json({ success: false, error: `Erro interno: ${error}` }, { status: 500 })
  }
}

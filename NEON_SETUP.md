# 🐘 Configuração do Banco de Dados Neon

Este guia detalha como configurar e integrar o banco de dados Neon PostgreSQL com o aplicativo de Mindscape.

## 📋 Pré-requisitos

- Conta no [Neon](https://neon.tech)
- Node.js 18+
- npm ou yarn

## 🚀 Passo 1: Criar Projeto no Neon

### 1.1 Acessar o Console Neon
1. Acesse [console.neon.tech](https://console.neon.tech)
2. Faça login ou crie uma conta
3. Clique em "Create Project"

### 1.2 Configurar o Projeto
\`\`\`
Project Name: mindmap-ia
Database Name: mindmap_db
Region: us-east-1 (ou mais próxima)
PostgreSQL Version: 15 (recomendado)
\`\`\`

### 1.3 Obter String de Conexão
Após criar o projeto, você receberá uma string de conexão similar a:
\`\`\`
postgresql://username:<EMAIL>/mindmap_db?sslmode=require
\`\`\`

## 🔧 Passo 2: Configurar Variáveis de Ambiente

### 2.1 Criar arquivo .env.local
\`\`\`bash
cp .env.example .env.local
\`\`\`

### 2.2 Configurar variáveis
\`\`\`env
# String de conexão principal
NEON_DATABASE_URL=postgresql://username:<EMAIL>/mindmap_db?sslmode=require

# Strings alternativas (para compatibilidade)
DATABASE_URL=postgresql://username:<EMAIL>/mindmap_db?sslmode=require
POSTGRES_URL=postgresql://username:<EMAIL>/mindmap_db?sslmode=require
\`\`\`

## 📦 Passo 3: Instalar Dependências

\`\`\`bash
npm install @neondatabase/serverless drizzle-orm drizzle-kit
\`\`\`

## 🗃️ Passo 4: Executar Migrações

### 4.1 Gerar migrações
\`\`\`bash
npm run db:generate
\`\`\`

### 4.2 Aplicar migrações
\`\`\`bash
npm run db:migrate
\`\`\`

### 4.3 Verificar estrutura (opcional)
\`\`\`bash
npm run db:studio
\`\`\`

## ✅ Passo 5: Testar Conexão

### 5.1 Via API
\`\`\`bash
curl http://localhost:3000/api/database/health
\`\`\`

### 5.2 Via código
\`\`\`typescript
import { testConnection } from '@/lib/neon-database'

const result = await testConnection()
console.log(result)
\`\`\`

## 🔄 Passo 6: Migração de Dados (se necessário)

### 6.1 Backup dos dados SQLite existentes
\`\`\`bash
curl -X POST http://localhost:3000/api/database/backup \
  -H "Content-Type: application/json" \
  -d '{"action": "create"}'
\`\`\`

### 6.2 Script de migração
\`\`\`typescript
// scripts/migrate-to-neon.ts
import { MindMapService, ConfigurationService } from '@/lib/database-service'
import fs from 'fs'

async function migrateFromSQLite() {
  // Ler backup SQLite
  const sqliteBackup = JSON.parse(fs.readFileSync('sqlite-backup.json', 'utf8'))
  
  // Migrar configurações
  for (const config of sqliteBackup.configurations) {
    await ConfigurationService.set(config.chave, config.valor, config.descricao)
  }
  
  // Migrar mapas mentais
  for (const map of sqliteBackup.mindMaps) {
    await MindMapService.create({
      nome: map.nome,
      descricao: map.descricao,
      nos: JSON.parse(map.nos),
      conexoes: JSON.parse(map.conexoes),
      password: map.hash_senha ? 'migrated' : undefined
    })
  }
  
  console.log('✅ Migração concluída!')
}

migrateFromSQLite().catch(console.error)
\`\`\`

## 🚀 Deploy no Vercel

### 7.1 Configurar variáveis no Vercel
\`\`\`bash
vercel env add NEON_DATABASE_URL
vercel env add DATABASE_URL
vercel env add POSTGRES_URL
\`\`\`

### 7.2 Deploy
\`\`\`bash
vercel --prod
\`\`\`

## 📊 Monitoramento e Manutenção

### 8.1 Verificar saúde do banco
\`\`\`bash
# Localmente
curl http://localhost:3000/api/database/health

# Produção
curl https://your-app.vercel.app/api/database/health
\`\`\`

### 8.2 Estatísticas
\`\`\`bash
curl http://localhost:3000/api/database/stats
\`\`\`

### 8.3 Backup automático
\`\`\`bash
curl -X POST http://localhost:3000/api/database/backup \
  -H "Content-Type: application/json" \
  -d '{"action": "create"}'
\`\`\`

## 🔒 Segurança

### 9.1 Configurações recomendadas
- ✅ SSL sempre habilitado (`sslmode=require`)
- ✅ Senhas criptografadas com bcrypt
- ✅ Validação de entrada robusta
- ✅ Rate limiting nas APIs
- ✅ Logs de auditoria

### 9.2 Backup e recuperação
- ✅ Backups automáticos diários
- ✅ Retenção de 30 dias
- ✅ Teste de restauração mensal
- ✅ Monitoramento de integridade

## 🐛 Solução de Problemas

### Erro de Conexão
\`\`\`bash
# Testar conexão direta
psql "postgresql://username:<EMAIL>/mindmap_db?sslmode=require"
\`\`\`

### Erro de SSL
Certifique-se de que `sslmode=require` está na string de conexão.

### Timeout de Conexão
Verifique se a região do Neon está próxima ao seu deploy.

### Migrações falhando
\`\`\`bash
# Reset e recriar
npm run db:reset
npm run db:generate
npm run db:migrate
\`\`\`

## 📈 Otimização de Performance

### 10.1 Índices
O schema já inclui índices otimizados para:
- Busca por nome de mapa
- Filtros por data
- Consultas por tags
- Logs de auditoria

### 10.2 Pool de conexões
\`\`\`typescript
// Configuração automática no código
const maxConnections = process.env.MAX_CONNECTIONS || 10
\`\`\`

### 10.3 Cache de queries
\`\`\`typescript
// Implementado via DatabaseMetrics
const cachedResult = await DatabaseMetrics.measure('operation', async () => {
  return await expensiveQuery()
})
\`\`\`

## 📚 Recursos Adicionais

- [Documentação Neon](https://neon.tech/docs)
- [Drizzle ORM](https://orm.drizzle.team)
- [PostgreSQL Docs](https://www.postgresql.org/docs/)
- [Vercel Postgres](https://vercel.com/docs/storage/vercel-postgres)

## 🆘 Suporte

Em caso de problemas:
1. Verifique os logs: `vercel logs`
2. Teste a conexão: `/api/database/health`
3. Consulte a documentação do Neon
4. Abra uma issue no repositório

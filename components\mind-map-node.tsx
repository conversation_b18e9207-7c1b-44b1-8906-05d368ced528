"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import type { MindMapNode as Node } from "@/types/mindmap"
import { Textarea } from "@/components/ui/textarea"
import { X, Edit3, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react"
import { useGuestLimits } from "@/components/guest-limits-provider"
import { useAuth } from "@/components/auth/auth-provider"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import useMindMapStore from "@/lib/store"

interface MindMapNodeProps {
  node: Node
  isSelected: boolean
  isConnecting: boolean
  onClick: () => void
  onDragStart: (e: React.MouseEvent) => void
  onDelete: () => void
  onConnectionStart: () => void
  onConnectionEnd: () => void
  onResizeStart: (e: React.MouseEvent) => void
}

export function MindMapNode({
  node,
  isSelected,
  isConnecting,
  onClick,
  onDragStart,
  onDelete,
  onConnectionStart,
  onConnectionEnd,
  onResizeStart,
}: MindMapNodeProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(node.content)
  const inputRef = useRef<HTMLInputElement>(null)
  const { updateNode, addChildNode, addSiblingNode, deleteNode: deleteNodeFromStore } = useMindMapStore()
  const { setIconRequest } = useMindMapStore()
  const [style, setStyle] = useState<"filled" | "outline">(node.style ?? "filled");
  const { useIntelligentFeature, intelligentUsesLeft } = useGuestLimits()
  const { user } = useAuth()

  const colors = [
    "#3b82f6", // azul
    "#ef4444", // vermelho
    "#10b981", // verde
    "#f59e0b", // amarelo
    "#8b5cf6", // roxo
    "#ec4899", // rosa
    "#06b6d4", // ciano
    "#84cc16", // lima
    "#f97316", // laranja
    "#6366f1", // índigo
    "#14b8a6", // teal
    "#a855f7"  // violeta
  ]

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleEdit = () => {
    setIsEditing(true)
    setEditContent(node.content)
  }

  const handleSaveEdit = () => {
    if (editContent.trim()) {
      updateNode(node.id, { content: editContent.trim() })
    }
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault()
      handleSaveEdit()
    } else if (e.key === "Escape") {
      setIsEditing(false)
      setEditContent(node.content)
    }
    // Enter normal agora cria nova linha
  }

  const nodeSize = {
    small: { width: 120, height: 60 },
    medium: { width: 160, height: 80 },
    large: { width: 200, height: 100 },
  }

  const size = nodeSize[node.size ?? "medium"]

  // Função para quebrar texto em linhas
  const wrapText = (text: string, maxWidth: number, fontSize: number = 12) => {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''

    // Estimativa mais precisa baseada no tamanho da fonte
    const charWidth = fontSize * 0.6 // Aproximação para fontes sans-serif
    const maxCharsPerLine = Math.floor((maxWidth - 16) / charWidth) // -16 para padding

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word

      if (testLine.length <= maxCharsPerLine) {
        currentLine = testLine
      } else {
        if (currentLine) {
          lines.push(currentLine)
          currentLine = word
        } else {
          // Palavra muito longa, quebra forçadamente
          if (word.length > maxCharsPerLine) {
            lines.push(word.substring(0, maxCharsPerLine - 3) + '...')
            currentLine = ''
          } else {
            currentLine = word
          }
        }
      }
    }
    if (currentLine) {
      lines.push(currentLine)
    }
    return lines
  }

  // Ajustar tamanho da fonte baseado no comprimento do texto
  const textLength = (node.content ?? '').length
  const baseFontSize = textLength > 100 ? 9 : textLength > 50 ? 10 : textLength > 25 ? 11 : 12

  // Calcular linhas de texto com o tamanho de fonte ajustado
  const textLines = wrapText(node.content ?? '', size.width, baseFontSize)
  const lineHeight = baseFontSize + 4 // Espaçamento entre linhas
  const maxLines = Math.floor((size.height - 16) / lineHeight) // -16 para padding vertical
  const displayLines = textLines.slice(0, maxLines)

  // Se há mais linhas do que cabem, adiciona "..." na última linha
  if (textLines.length > maxLines && displayLines.length > 0) {
    const lastLine = displayLines[displayLines.length - 1]
    const maxCharsInLastLine = Math.floor((size.width - 16) / (baseFontSize * 0.6)) - 3
    displayLines[displayLines.length - 1] = lastLine.length > maxCharsInLastLine ?
      lastLine.substring(0, maxCharsInLastLine) + '...' : lastLine + '...'
  }

  const fontSize = baseFontSize

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <g>
          {/* Fundo do Nó */}
          <rect
            x={node.x - size.width / 2}
            y={node.y - size.height / 2}
            width={size.width}
            height={size.height}
            rx="12"
            fill={style === "filled" ? node.color : "transparent"}
            stroke={isSelected ? "#1f2937" : "transparent"}
            strokeWidth={isSelected ? "3" : "0"}
            className="cursor-pointer drop-shadow-md"
            onMouseDown={onDragStart}
            onClick={onClick}
            onDoubleClick={handleEdit}
            onMouseEnter={isConnecting ? onConnectionEnd : undefined}
          />

          {/* Conteúdo do Nó */}
          {isEditing ? (
            <foreignObject
              x={node.x - size.width / 2 + 8}
              y={node.y - size.height / 2 + 8}
              width={size.width - 16}
              height={size.height - 16}
            >
              <Textarea
                ref={inputRef as any}
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onBlur={handleSaveEdit}
                onKeyDown={handleKeyPress}
                className="text-xs px-2 py-1 bg-white/95 border-0 resize-none w-full h-full"
                style={{ fontSize: '11px', lineHeight: '1.3' }}
              />
            </foreignObject>
          ) : (
            <>
              {/* Renderizar múltiplas linhas de texto */}
              {displayLines.map((line, index) => {
                const lineHeight = fontSize + 4
                const totalHeight = displayLines.length * lineHeight
                const startY = node.y - (totalHeight / 2) + (lineHeight / 2)
                const lineY = startY + (index * lineHeight)
                const isFirstLine = index === 0
                const hasIcon = node.icon && !(node.icon.startsWith("http") || node.icon.startsWith("data:"))

                return (
                  <text
                    key={index}
                    x={node.x}
                    y={lineY}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fill={style === "filled" ? "white" : "#374151"}
                    fontSize={fontSize}
                    fontWeight="500"
                    className="pointer-events-none select-none"
                  >
                    {isFirstLine && hasIcon ? `${node.icon} ${line}` : line}
                  </text>
                )
              })}

              {/* Ícone como imagem (se for URL) */}
              {node.icon && (node.icon.startsWith("http") || node.icon.startsWith("data:")) && (
                <image
                  href={node.icon}
                  x={node.x - 8}
                  y={node.y - (displayLines.length * 8) - 8}
                  width="16"
                  height="16"
                />
              )}
            </>
          )}

      {/* Controles do Nó (visível quando selecionado) */}
      {isSelected && !isEditing && (
        <g>
          {/* Botão Editar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y - size.height / 2 + 15}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              handleEdit()
            }}
          />
          <Edit3
            x={node.x + size.width / 2 - 21}
            y={node.y - size.height / 2 + 9}
            width="12"
            height="12"
            className="pointer-events-none"
          />

          {/* Botão Conectar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              onConnectionStart()
            }}
          />
          <Link
            x={node.x + size.width / 2 - 21}
            y={node.y - 6}
            width="12"
            height="12"
            className="pointer-events-none"
          />

          {/* Botão Sugestões Inteligentes */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y + size.height / 2 - 15}
            r="12"
            fill="white"
            stroke={!user && intelligentUsesLeft === 0 ? "#ef4444" : "#e5e7eb"}
            strokeWidth="1"
            className={!user && intelligentUsesLeft === 0 ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
            onClick={(e) => {
              e.stopPropagation()
              // Verificar limitação antes de usar
              if (useIntelligentFeature()) {
                // Dispara fluxo de sugestão de conexões via chat
                setIconRequest({ nodeId: node.id, prompt: `Sugira conexões conceituais profundas e relevantes com outros nós deste mapa mental, com base no conteúdo deste nó: ${node.content}` })
              }
            }}

          />
          <Sparkles
            x={node.x + size.width / 2 - 21}
            y={node.y + size.height / 2 - 21}
            width="12"
            height="12"
            className="pointer-events-none"
            fill={!user && intelligentUsesLeft === 0 ? "#ef4444" : "#6b7280"}
          />

          {/* Botão Deletar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y + size.height / 2 + 15}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
          />
          <X
            x={node.x + size.width / 2 - 21}
            y={node.y + size.height / 2 + 9}
            width="12"
            height="12"
            className="pointer-events-none text-red-500"
          />

          {/* Opção de estilo pontilhado */}
          <rect
            x={node.x - size.width / 2 + 15}
            y={node.y + size.height / 2 + 25}
            width="16"
            height="16"
            fill="transparent"
            stroke="#6b7280"
            strokeWidth="2"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              const newStyle = style === "filled" ? "outline" : "filled"
              setStyle(newStyle)
              updateNode(node.id, { style: newStyle })
            }}
          />

          {/* Paleta de Cores */}
          {colors.map((color, index) => {
            const row = Math.floor(index / 6);
            const col = index % 6;
            return (
              <circle
                key={color}
                cx={node.x - size.width / 2 + 40 + col * 18}
                cy={node.y + size.height / 2 + 25 + row * 20}
                r="7"
                fill={color}
                stroke={node.color === color ? "#1f2937" : "#e5e7eb"}
                strokeWidth={node.color === color ? "2" : "1"}
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  updateNode(node.id, { color })
                }}
              />
            );
          })}

          {/* Handle de resize */}
          <rect
            x={node.x + size.width / 2 - 8}
            y={node.y + size.height / 2 - 8}
            width={8}
            height={8}
            fill="white"
            stroke="#6b7280"
            strokeWidth="1"
            className="cursor-se-resize"
            onMouseDown={(e) => {
              e.stopPropagation()
              onResizeStart(e)
            }}
          />
        </g>
      )}
        </g>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={() => addChildNode(node.id)}>Adicionar filho</ContextMenuItem>
        <ContextMenuItem onClick={() => addSiblingNode(node.id)}>Adicionar irmão</ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={() => deleteNodeFromStore(node.id)}>Excluir nó</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}

"use client";

import { useState, useRef, useEffect } from "react";
import { useChat } from "ai/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Send, Bot, User, Loader2 } from "lucide-react";
import useMindMapStore from "@/lib/store";
import { nanoid } from "nanoid";

export function ChatInterface() {
  const { nodes, selectedNodeId, addNode, addConnection, mindMapId } = useMindMapStore();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: chatLoading,
    setMessages,
    append,
  } = useChat({
    api: "/api/chat",
    body: {
      selectedNodeId,
      nodes,
      mindMapId,
    },
    // A API retorna JSON completo como string; indicamos ao hook para tratá-lo como texto simples.
    streamProtocol: "text",
    onFinish: (message) => {
      let answerText = "";
      try {
        const jsonString = message.content.substring(
          message.content.indexOf("{"),
          message.content.lastIndexOf("}") + 1
        );
        const parsed = JSON.parse(jsonString);

        answerText = parsed.answer ?? parsed.text ?? "";

        const newNodes: any[] = Array.isArray(parsed)
          ? []
          : parsed.newNodes || parsed.nodes || parsed.children || [];

        // Atualiza mensagem do LLM com a resposta humana
        if (!answerText) {
          answerText = newNodes.length
            ? `Sugeri ${newNodes.length} ideias.`
            : "Não encontrei novas ideias, mas tente reformular sua pergunta.";
        }

        setMessages((prev) =>
          prev.map((m) => (m.id === message.id ? { ...m, content: answerText } : m))
        );

        if (selectedNodeId && newNodes.length > 0) {
          setPendingNodes({ parentId: selectedNodeId, nodes: newNodes });
        }

        const icons: string[] = parsed.icons || [];
        if (icons.length && selectedNodeId) {
          setPendingIcons({ parentId: selectedNodeId, icons });
        }
      } catch (error) {
        console.error("Error parsing AI response:", error);
        setMessages((prev) =>
          prev.map((m) => (m.id === message.id ? { ...m, content: "(falha ao interpretar resposta)" } : m))
        );
      }
    },
  });

  const { iconRequest, setIconRequest, updateNode } = useMindMapStore();

  // pendentes de ícone
  const [pendingIcons, setPendingIcons] = useState<
    | { parentId: string; icons: string[] }
    | null
  >(null);

  // disparar mensagem automática para ícone
  useEffect(() => {
    if (iconRequest) {
      append(
        { role: "user", content: iconRequest.prompt },
        { body: { selectedNodeId: iconRequest.nodeId } },
      );
      setIconRequest(null);
    }
  }, [iconRequest]);

  // Estado para guardar sugestões do LLM aguardando confirmação do usuário
  const [pendingNodes, setPendingNodes] = useState<
    | { parentId: string; nodes: { content: string; icon?: string }[] }
    | null
  >(null);

  // conjunto de índices dos nós selecionados para adicionar
  const [selectedToAdd, setSelectedToAdd] = useState<Set<number>>(new Set());

  // sempre que novas sugestões chegam, seleciona todas por padrão
  useEffect(() => {
    if (pendingNodes) {
      setSelectedToAdd(new Set(pendingNodes.nodes.map((_, idx) => idx)));
    } else {
      setSelectedToAdd(new Set());
    }
  }, [pendingNodes]);

  const handleAcceptNodes = () => {
    if (!pendingNodes) return;
    const { parentId, nodes: proposed } = pendingNodes;

    const chosen = proposed.filter((_, idx) => selectedToAdd.has(idx));
    if (chosen.length === 0) {
      setPendingNodes(null);
      return;
    }

    const parentNode = nodes.find((n) => n.id === parentId);
    if (!parentNode) {
      setPendingNodes(null);
      return;
    }

    // Filtra filhos já existentes para calcular espaçamento angular
    const existingChildren = nodes.filter((n) =>
      useMindMapStore.getState().connections.some((c) => c.from === parentId && c.to === n.id)
    );

    const total = existingChildren.length + chosen.length;
    const radius = 180;

    chosen.forEach((node, idx) => {
      const angle = ((existingChildren.length + idx) / total) * 2 * Math.PI;
      const newX = parentNode.x + Math.cos(angle) * radius;
      const newY = parentNode.y + Math.sin(angle) * radius;

      const newNodeId = nanoid();
      addNode({
        id: newNodeId,
        content: node.content,
        icon: node.icon,
        x: newX,
        y: newY,
        level: (parentNode.level ?? 0) + 1,
      });
      addConnection(parentId, newNodeId);
    });

    // Feedback no chat
    setMessages((prev) => [
      ...prev,
      {
        id: nanoid(),
        role: "assistant",
        content: `Adicionei ${chosen.length} ideias ao mapa com sucesso!`,
      } as any, // tipagem simplificada
    ]);

    setPendingNodes(null);

    // Salvar mapa atualizado
    saveCurrentMap();
  };

  const saveCurrentMap = async () => {
    try {
      const response = await fetch("/api/mindmaps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: mindMapId,
          titulo: "Mapa", // título placeholder; backend mantém se id existir
          dados: { nodes: useMindMapStore.getState().nodes, connections: useMindMapStore.getState().connections },
        }),
      });
      if (!response.ok) {
        console.error("Erro ao salvar mapa:", await response.text());
      }
    } catch (err) {
      console.error("Falha ao salvar mapa", err);
    }
  };

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const getPlaceholderText = () => {
    const selectedNode = nodes.find((n) => n.id === selectedNodeId);
    if (selectedNode) {
      return `Pergunte à IA sobre "${selectedNode.content}"...`;
    }
    return "Selecione um nó para conversar com a IA...";
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`flex gap-2 max-w-[80%] ${
                  message.role === "user" ? "flex-row-reverse" : "flex-row"
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.role === "user"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {message.role === "user" ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>
                <div
                  className={`rounded-lg px-3 py-2 ${
                    message.role === "user"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">
                    {message.content}
                  </p>
                </div>
              </div>
            </div>
          ))}

          {chatLoading && (
            <div className="flex gap-3 justify-start">
              <div className="flex gap-2">
                <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center">
                  <Bot className="w-4 h-4" />
                </div>
                <div className="bg-gray-100 rounded-lg px-3 py-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Confirmação para adicionar nós sugeridos */}
      {pendingNodes && (
        <div className="p-4 border-t bg-gray-50 flex flex-col gap-3">
          <span className="text-sm font-medium">Ideias sugeridas pelo assistente:</span>
          <ul className="text-sm space-y-1">
            {pendingNodes.nodes.map((n, idx) => (
              <li key={idx} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  className="h-4 w-4"
                  checked={selectedToAdd.has(idx)}
                  onChange={() => {
                    setSelectedToAdd((prev) => {
                      const next = new Set(prev);
                      if (next.has(idx)) next.delete(idx);
                      else next.add(idx);
                      return next;
                    });
                  }}
                />
                <span>{n.icon ? `${n.icon} ` : ""}{n.content}</span>
              </li>
            ))}
          </ul>
          <div className="flex justify-end gap-2">
            <Button variant="outline" size="sm" onClick={() => setPendingNodes(null)}>
              Cancelar
            </Button>
            <Button onClick={handleAcceptNodes} size="sm" disabled={selectedToAdd.size === 0}>
              Adicionar selecionados
            </Button>
          </div>
        </div>
      )}

      {pendingIcons && (
        <div className="p-4 border-t bg-gray-50 flex flex-col gap-3">
          <span className="text-sm font-medium">Escolha um ícone:</span>
          <div className="flex flex-wrap gap-2">
            {pendingIcons.icons.map((ico, idx) => (
              <button
                key={idx}
                className="w-10 h-10 flex items-center justify-center border rounded hover:bg-gray-100"
                onClick={() => {
                  if (ico === "sem ícone") {
                    updateNode(pendingIcons.parentId, { icon: "" });
                  } else {
                    updateNode(pendingIcons.parentId, { icon: ico });
                  }
                  setPendingIcons(null);
                }}
              >
                {ico.startsWith("http") || ico.startsWith("data:") ? (
                  <img src={ico} className="w-6 h-6" />
                ) : (
                  <span className="text-lg">{ico === "sem ícone" ? "🚫" : ico}</span>
                )}
              </button>
            ))}
          </div>
          <Button variant="outline" size="sm" onClick={() => setPendingIcons(null)}>Cancelar</Button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-100">
        <div className="flex gap-2">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder={getPlaceholderText()}
            disabled={chatLoading || !selectedNodeId}
            className="flex-1"
          />
          <Button
            type="submit"
            disabled={chatLoading || !input.trim() || !selectedNodeId}
            size="icon"
          >
            {chatLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

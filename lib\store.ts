import { create, StateCreator } from 'zustand';
import { MindMapNode, MindMapConnection } from '@/types/mindmap';

interface MindMapState {
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  selectedNodeId: string | null;
  mindMapId: string | null;
  setInitialState: (data: { nodes: MindMapNode[], connections: MindMapConnection[] }) => void;
  setMindMapId: (id: string) => void;
  selectNode: (nodeId: string | null) => void;
  addNode: (nodeData: Omit<MindMapNode, 'id'> & { id?: string }) => void;
  updateNode: (nodeId: string, updates: Partial<MindMapNode>) => void;
  deleteNode: (nodeId: string) => void;
  addChildNode: (parentId: string) => void;
  addSiblingNode: (nodeId: string) => void;
  addConnection: (fromId: string, toId: string) => void;

  iconRequest: { nodeId: string; prompt: string } | null;
  setIconRequest: (r: { nodeId: string; prompt: string } | null) => void;
}

const mindMapStateCreator: StateCreator<MindMapState> = (set, get) => ({
  nodes: [],
  connections: [],
  selectedNodeId: null,
  mindMapId: null,
  iconRequest: null,

  setMindMapId: (id) => set({ mindMapId: id }),

  setInitialState: (data) => set({
    nodes: data.nodes,
    connections: data.connections,
    selectedNodeId: null,
  }),

  selectNode: (nodeId) => set({ selectedNodeId: nodeId }),

  addNode: (nodeData) => set((state) => ({
    nodes: [...state.nodes, { id: crypto.randomUUID(), ...nodeData, x: nodeData.x ?? 0, y: nodeData.y ?? 0, level: nodeData.level ?? 0, content: nodeData.content ?? '' }]
  })),

  updateNode: (nodeId, updates) => set((state) => ({
    nodes: state.nodes.map(n => n.id === nodeId ? { ...n, ...updates } : n)
  })),

  deleteNode: (nodeId) => set((state) => {
    const newConnections = state.connections.filter(c => c.from !== nodeId && c.to !== nodeId);
    const newNodes = state.nodes.filter(n => n.id !== nodeId);
    
    return {
      nodes: newNodes,
      connections: newConnections,
      selectedNodeId: state.selectedNodeId === nodeId ? null : state.selectedNodeId
    };
  }),

  addChildNode: (parentId) => {
    const parentNode = get().nodes.find(n => n.id === parentId);
    if (!parentNode) return;

    const newNode: MindMapNode = {
      id: crypto.randomUUID(),
      content: 'New Node',
      x: parentNode.x + 150,
      y: parentNode.y + 50,
      level: parentNode.level + 1,
    };

    const newConnection: MindMapConnection = {
      from: parentId,
      to: newNode.id,
    };

    set((state) => ({
      nodes: [...state.nodes, newNode],
      connections: [...state.connections, newConnection],
    }));
  },

  addSiblingNode: (nodeId) => {
    const parentConnection = get().connections.find(c => c.to === nodeId);
    if (!parentConnection) return;
    get().addChildNode(parentConnection.from);
  },

  addConnection: (fromId, toId) => set((state) => ({
    connections: [...state.connections, { from: fromId, to: toId }]
  })),

  setIconRequest: (r) => set({ iconRequest: r }),
});

const useMindMapStore = create<MindMapState>(mindMapStateCreator);

export default useMindMapStore;
"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Download, ZoomIn, ZoomOut, RotateCcw, Maximize } from "lucide-react"
import { useTranslation } from "@/lib/i18n"

interface MindMapViewerProps {
  mindMap: {
    id: string
    name: string
    description?: string
    data: {
      nodes: Array<{
        id: string
        text: string
        x: number
        y: number
        type?: string
        color?: string
      }>
      connections: Array<{
        source: string
        target: string
      }>
    }
    created_at: Date
    updated_at: Date
  }
  onBack: () => void
}

export function MindMapViewer({ mindMap, onBack }: MindMapViewerProps) {
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const svgRef = useRef<SVGSVGElement>(null)
  const { t } = useTranslation()

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault()
    const delta = e.deltaY > 0 ? 0.9 : 1.1
    setZoom((prev) => Math.max(0.1, Math.min(3, prev * delta)))
  }

  const zoomIn = () => setZoom((prev) => Math.min(3, prev * 1.2))
  const zoomOut = () => setZoom((prev) => Math.max(0.1, prev / 1.2))
  const resetView = () => {
    setZoom(1)
    setPan({ x: 0, y: 0 })
  }

  const fitToScreen = () => {
    if (!mindMap.data.nodes.length) return

    const nodes = mindMap.data.nodes
    const minX = Math.min(...nodes.map((n) => n.x))
    const maxX = Math.max(...nodes.map((n) => n.x))
    const minY = Math.min(...nodes.map((n) => n.y))
    const maxY = Math.max(...nodes.map((n) => n.y))

    const width = maxX - minX + 200
    const height = maxY - minY + 200
    const centerX = (minX + maxX) / 2
    const centerY = (minY + maxY) / 2

    const scaleX = 800 / width
    const scaleY = 600 / height
    const scale = Math.min(scaleX, scaleY, 1)

    setZoom(scale)
    setPan({
      x: 400 - centerX * scale,
      y: 300 - centerY * scale,
    })
  }

  const exportAsPNG = () => {
    if (!svgRef.current) return

    const svg = svgRef.current
    const svgData = new XMLSerializer().serializeToString(svg)
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    const img = new Image()

    canvas.width = 1200
    canvas.height = 800

    img.onload = () => {
      if (ctx) {
        ctx.fillStyle = "white"
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(img, 0, 0)

        const link = document.createElement("a")
        link.download = `${mindMap.name}.png`
        link.href = canvas.toDataURL()
        link.click()
      }
    }

    img.src = "data:image/svg+xml;base64," + btoa(unescape(encodeURIComponent(svgData)))
  }

  const exportAsJSON = () => {
    const dataStr = JSON.stringify(mindMap.data, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `${mindMap.name}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  useEffect(() => {
    fitToScreen()
  }, [mindMap])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button variant="ghost" size="sm" onClick={onBack} className="mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t.viewer.backToList}
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{mindMap.name}</h1>
                {mindMap.description && <p className="text-sm text-gray-500">{mindMap.description}</p>}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={exportAsPNG}>
                <Download className="h-4 w-4 mr-2" />
                {t.viewer.exportPng}
              </Button>
              <Button variant="outline" size="sm" onClick={exportAsJSON}>
                <Download className="h-4 w-4 mr-2" />
                {t.viewer.exportJson}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
          <div className="space-y-4">
            {/* Mind Map Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Informações</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">{t.viewer.createdAt}:</span>
                  <span>{new Date(mindMap.created_at).toLocaleDateString("pt-BR")}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">{t.viewer.updatedAt}:</span>
                  <span>{new Date(mindMap.updated_at).toLocaleDateString("pt-BR")}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">{t.viewer.nodes}:</span>
                  <Badge variant="secondary">{mindMap.data.nodes.length}</Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">{t.viewer.connections}:</span>
                  <Badge variant="secondary">{mindMap.data.connections.length}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Controls */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{t.viewer.zoom}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Zoom:</span>
                  <span className="text-sm font-mono">{Math.round(zoom * 100)}%</span>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={zoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={zoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={resetView}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={fitToScreen}>
                    <Maximize className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Node List */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Nós do Mapa</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {mindMap.data.nodes.map((node) => (
                    <div key={node.id} className="flex items-center gap-2 p-2 rounded-lg bg-gray-50 text-sm">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: node.color || "#3b82f6" }} />
                      <span className="flex-1 truncate">{node.text}</span>
                      <Badge variant="outline" className="text-xs">
                        {node.type || "node"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 relative overflow-hidden">
          <svg
            ref={svgRef}
            className="w-full h-full cursor-move"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onWheel={handleWheel}
            style={{ backgroundColor: "#fafafa" }}
          >
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
              </marker>
            </defs>

            <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
              {/* Render connections */}
              {mindMap.data.connections.map((connection, index) => {
                const sourceNode = mindMap.data.nodes.find((n) => n.id === connection.source)
                const targetNode = mindMap.data.nodes.find((n) => n.id === connection.target)

                if (!sourceNode || !targetNode) return null

                return (
                  <line
                    key={index}
                    x1={sourceNode.x}
                    y1={sourceNode.y}
                    x2={targetNode.x}
                    y2={targetNode.y}
                    stroke="#6b7280"
                    strokeWidth="2"
                    markerEnd="url(#arrowhead)"
                  />
                )
              })}

              {/* Render nodes */}
              {mindMap.data.nodes.map((node) => (
                <g key={node.id}>
                  <circle
                    cx={node.x}
                    cy={node.y}
                    r={node.type === "central" ? 40 : 30}
                    fill={node.color || "#3b82f6"}
                    stroke="white"
                    strokeWidth="3"
                    className="drop-shadow-lg"
                  />
                  <text
                    x={node.x}
                    y={node.y}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fill="white"
                    fontSize={node.type === "central" ? "14" : "12"}
                    fontWeight="600"
                    className="pointer-events-none select-none"
                  >
                    {node.text.length > 15 ? `${node.text.substring(0, 15)}...` : node.text}
                  </text>
                </g>
              ))}
            </g>
          </svg>

          {/* Zoom indicator */}
          <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg px-3 py-2 text-sm font-mono">
            {Math.round(zoom * 100)}%
          </div>
        </div>
      </div>
    </div>
  )
}

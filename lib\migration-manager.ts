import { getSqlConnection } from "./neon-connection"

export interface Migration {
  id: string
  name: string
  version: string
  up: () => Promise<void>
  down: () => Promise<void>
}

export class MigrationManager {
  private migrations: Migration[] = []

  constructor() {
    this.registerMigrations()
  }

  private registerMigrations() {
    // Migration 001: Tabelas iniciais
    this.migrations.push({
      id: "001",
      name: "create_initial_tables",
      version: "1.0.0",
      up: async () => {
        const connection = await getSqlConnection("migration")

        await connection`
          CREATE TABLE IF NOT EXISTS configuracoes (
            id SERIAL PRIMARY KEY,
            chave VARCHAR(255) UNIQUE NOT NULL,
            valor TEXT NOT NULL,
            descricao TEXT,
            criado_em TIMESTAMP DEFAULT NOW(),
            atualizado_em TIMESTAMP DEFAULT NOW()
          )
        `

        await connection`
          CREATE TABLE IF NOT EXISTS mapas_mentais (
            id VARCHAR(255) PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            descricao TEXT DEFAULT '',
            nos JSONB NOT NULL DEFAULT '[]',
            conexoes JSONB NOT NULL DEFAULT '[]',
            protegido_senha BOOLEAN DEFAULT FALSE,
            hash_senha VARCHAR(255),
            tags JSONB DEFAULT '[]',
            versao INTEGER DEFAULT 1,
            metadados JSONB DEFAULT '{}',
            criado_em TIMESTAMP DEFAULT NOW(),
            atualizado_em TIMESTAMP DEFAULT NOW(),
            acessado_em TIMESTAMP DEFAULT NOW()
          )
        `

        await connection`
          CREATE TABLE IF NOT EXISTS logs_auditoria (
            id SERIAL PRIMARY KEY,
            acao VARCHAR(100) NOT NULL,
            recurso VARCHAR(100) NOT NULL,
            recurso_id VARCHAR(255) NOT NULL,
            dados_antigos JSONB,
            dados_novos JSONB,
            metadados JSONB DEFAULT '{}',
            criado_em TIMESTAMP DEFAULT NOW()
          )
        `

        await connection`
          CREATE TABLE IF NOT EXISTS metricas (
            id SERIAL PRIMARY KEY,
            operacao VARCHAR(100) NOT NULL,
            tempo_execucao INTEGER NOT NULL,
            sucesso BOOLEAN NOT NULL,
            erro TEXT,
            metadados JSONB DEFAULT '{}',
            criado_em TIMESTAMP DEFAULT NOW()
          )
        `
      },
      down: async () => {
        const connection = await getSqlConnection("migration")

        await connection`DROP TABLE IF EXISTS metricas`
        await connection`DROP TABLE IF EXISTS logs_auditoria`
        await connection`DROP TABLE IF EXISTS mapas_mentais`
        await connection`DROP TABLE IF EXISTS configuracoes`
      },
    })

    // Migration 002: Índices para performance
    this.migrations.push({
      id: "002",
      name: "create_indexes",
      version: "1.0.1",
      up: async () => {
        const connection = await getSqlConnection("migration")

        await connection`CREATE INDEX IF NOT EXISTS idx_configuracoes_chave ON configuracoes(chave)`
        await connection`CREATE INDEX IF NOT EXISTS idx_mapas_mentais_nome ON mapas_mentais(nome)`
        await connection`CREATE INDEX IF NOT EXISTS idx_mapas_mentais_criado_em ON mapas_mentais(criado_em)`
        await connection`CREATE INDEX IF NOT EXISTS idx_mapas_mentais_atualizado_em ON mapas_mentais(atualizado_em)`
        await connection`CREATE INDEX IF NOT EXISTS idx_logs_auditoria_recurso ON logs_auditoria(recurso, recurso_id)`
        await connection`CREATE INDEX IF NOT EXISTS idx_metricas_operacao ON metricas(operacao)`
        await connection`CREATE INDEX IF NOT EXISTS idx_metricas_criado_em ON metricas(criado_em)`
      },
      down: async () => {
        const connection = await getSqlConnection("migration")

        await connection`DROP INDEX IF EXISTS idx_metricas_criado_em`
        await connection`DROP INDEX IF EXISTS idx_metricas_operacao`
        await connection`DROP INDEX IF EXISTS idx_logs_auditoria_recurso`
        await connection`DROP INDEX IF EXISTS idx_mapas_mentais_atualizado_em`
        await connection`DROP INDEX IF EXISTS idx_mapas_mentais_criado_em`
        await connection`DROP INDEX IF EXISTS idx_mapas_mentais_nome`
        await connection`DROP INDEX IF EXISTS idx_configuracoes_chave`
      },
    })
  }

  async runMigrations(): Promise<void> {
    console.log("🔄 Executando migrações...")

    // Criar tabela de controle de migrações
    await this.createMigrationTable()

    // Obter migrações já executadas
    const executedMigrations = await this.getExecutedMigrations()

    // Executar migrações pendentes
    for (const migration of this.migrations) {
      if (!executedMigrations.includes(migration.id)) {
        console.log(`  Executando migração ${migration.id}: ${migration.name}`)

        try {
          await migration.up()
          await this.recordMigration(migration)
          console.log(`  ✅ Migração ${migration.id} executada com sucesso`)
        } catch (error) {
          console.error(`  ❌ Erro na migração ${migration.id}:`, error)
          throw error
        }
      }
    }

    console.log("✅ Todas as migrações foram executadas")
  }

  private async createMigrationTable(): Promise<void> {
    const connection = await getSqlConnection("migration")

    await connection`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        executed_at TIMESTAMP DEFAULT NOW()
      )
    `
  }

  private async getExecutedMigrations(): Promise<string[]> {
    const connection = await getSqlConnection("migration")

    const result = await connection`
      SELECT id FROM schema_migrations ORDER BY executed_at
    `

    return result.map((row: any) => row.id)
  }

  private async recordMigration(migration: Migration): Promise<void> {
    const connection = await getSqlConnection("migration")

    await connection`
      INSERT INTO schema_migrations (id, name, version)
      VALUES (${migration.id}, ${migration.name}, ${migration.version})
    `
  }

  async rollbackMigration(migrationId: string): Promise<void> {
    const migration = this.migrations.find((m) => m.id === migrationId)
    if (!migration) {
      throw new Error(`Migração ${migrationId} não encontrada`)
    }

    console.log(`🔄 Revertendo migração ${migrationId}: ${migration.name}`)

    try {
      await migration.down()

      const connection = await getSqlConnection("migration")
      await connection`DELETE FROM schema_migrations WHERE id = ${migrationId}`

      console.log(`✅ Migração ${migrationId} revertida com sucesso`)
    } catch (error) {
      console.error(`❌ Erro ao reverter migração ${migrationId}:`, error)
      throw error
    }
  }

  async getMigrationStatus(): Promise<{
    total: number
    executed: number
    pending: string[]
  }> {
    const executedMigrations = await this.getExecutedMigrations()
    const pendingMigrations = this.migrations.filter((m) => !executedMigrations.includes(m.id)).map((m) => m.id)

    return {
      total: this.migrations.length,
      executed: executedMigrations.length,
      pending: pendingMigrations,
    }
  }
}

// Instância global do gerenciador de migrações
export const migrationManager = new MigrationManager()

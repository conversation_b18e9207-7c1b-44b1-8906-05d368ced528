import { type NextRequest, NextResponse } from "next/server"
import { getMindMaps, saveMindMap } from "@/lib/database"
import { getUserFromRequest } from "@/lib/middleware"

export async function GET(request: NextRequest) {
  try {
    // Get user from request (optional for backward compatibility)
    const user = getUserFromRequest(request)
    const userId = user?.userId

    const mindMapsFromDb = await getMindMaps(userId)
    const mindMaps = mindMapsFromDb.map((m: any) => ({
      id: m.id,
      titulo: m.name,
      dados: m.data,
      criado_em: new Date(m.created_at).toISOString(),
      atualizado_em: new Date(m.updated_at).toISOString(),
      descricao: m.description,
      user_id: m.user_id,
    }))
    return NextResponse.json({ mindMaps })
  } catch (error) {
    console.error("Error getting mind maps:", error)
    return NextResponse.json({ error: "Failed to get mind maps" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, titulo, descricao, dados } = body

    if (!titulo || !dados) {
      return NextResponse.json({ error: "Título e dados são obrigatórios" }, { status: 400 })
    }

    // Get user from request (optional for backward compatibility)
    const user = getUserFromRequest(request)
    const userId = user?.userId

    const savedId = await saveMindMap({
      id,
      name: titulo,
      description: descricao,
      data: dados,
      user_id: userId,
    })

    // Return the complete mind map object that the frontend expects
    const mindMap = {
      id: savedId,
      titulo: titulo,
      dados: dados,
      descricao: descricao,
      user_id: userId,
      criado_em: new Date().toISOString(),
      atualizado_em: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      id: savedId,
      mindMap: mindMap
    })
  } catch (error) {
    console.error("Error saving mind map:", error)
    return NextResponse.json({ error: "Failed to save mind map" }, { status: 500 })
  }
}

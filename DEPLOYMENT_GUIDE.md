# 🚀 Guia de Deploy - Mind Map IA

Este guia detalha o processo completo de deploy da aplicação Mind Map IA no Vercel com banco de dados Neon.

## 📋 Pré-requisitos

### 1. <PERSON><PERSON> Necessárias
- [Vercel Account](https://vercel.com)
- [Neon Database Account](https://neon.tech)
- [GitHub Account](https://github.com) (para CI/CD)

### 2. Ferramentas Locais
\`\`\`bash
npm install -g vercel
npm install -g @vercel/cli
\`\`\`

## 🔧 Configuração Inicial

### 1. Configurar Neon Database

\`\`\`bash
# 1. Criar projeto no Neon
# Acesse: https://console.neon.tech
# Clique em "Create Project"
# Escolha região próxima ao Vercel (us-east-1 recomendado)

# 2. Obter connection string
# Formato: ************************************************************
\`\`\`

### 2. Configurar Variáveis de Ambiente

#### Localmente (.env.local):
\`\`\`bash
# Database
NEON_DATABASE_URL="************************************************************"
VERCEL_REGION="us-east-1"
MAX_CONNECTIONS="10"
CONNECTION_TIMEOUT="30000"
QUERY_TIMEOUT="10000"

# Security
SESSION_SECRET="your-32-character-session-secret-here"
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# AI Providers (opcional)
OPENROUTER_API_KEY="sk-or-v1-your-key-here"
OLLAMA_BASE_URL="http://localhost:11434"

# Application
NODE_ENV="production"
LOG_LEVEL="info"

# Features
METRICS_ENABLED="true"
BACKUP_ENABLED="true"
RATE_LIMIT_ENABLED="true"
\`\`\`

#### No Vercel:
\`\`\`bash
# Configurar via CLI
vercel env add NEON_DATABASE_URL
vercel env add SESSION_SECRET
vercel env add ENCRYPTION_KEY
vercel env add VERCEL_REGION
vercel env add MAX_CONNECTIONS

# Ou via Dashboard
# https://vercel.com/dashboard -> Projeto -> Settings -> Environment Variables
\`\`\`

## 🏗️ Processo de Deploy

### 1. Verificações Pré-Deploy

\`\`\`bash
# Instalar dependências
npm install

# Executar verificações
npm run deploy:check

# Testar banco de dados
npm run test:database

# Build local para verificar erros
npm run build
\`\`\`

### 2. Deploy Inicial

\`\`\`bash
# Login no Vercel
vercel login

# Deploy inicial
vercel --prod

# Ou usando GitHub Actions (recomendado)
git push origin main
\`\`\`

### 3. Configuração Pós-Deploy

\`\`\`bash
# Executar migrações (automático no primeiro deploy)
# Verificar logs: vercel logs

# Testar aplicação
curl https://your-app.vercel.app/api/health

# Verificar métricas
curl https://your-app.vercel.app/api/database/stats
\`\`\`

## 🔍 Verificações de Saúde

### 1. Endpoints de Monitoramento

\`\`\`bash
# Health check geral
GET /api/health

# Status do banco de dados
GET /api/database/health

# Estatísticas do sistema
GET /api/database/stats

# Métricas de performance
GET /api/metrics
\`\`\`

### 2. Logs e Debugging

\`\`\`bash
# Ver logs em tempo real
vercel logs --follow

# Logs específicos de função
vercel logs --function=api/chat

# Logs de build
vercel logs --build
\`\`\`

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão com Banco
\`\`\`bash
# Verificar variáveis
vercel env ls

# Testar conexão
npm run test:database

# Verificar região
# Neon e Vercel devem estar na mesma região
\`\`\`

#### 2. Timeout em Queries
\`\`\`bash
# Aumentar timeout
vercel env add QUERY_TIMEOUT 30000

# Verificar índices do banco
npm run db:analyze
\`\`\`

#### 3. Erro de Build
\`\`\`bash
# Limpar cache
vercel --force

# Verificar dependências
npm audit fix

# Build local
npm run build
\`\`\`

#### 4. Problemas de Performance
\`\`\`bash
# Verificar métricas
curl https://your-app.vercel.app/api/metrics

# Analisar logs
vercel logs --since=1h

# Verificar pool de conexões
# Ajustar MAX_CONNECTIONS se necessário
\`\`\`

## 📊 Monitoramento Contínuo

### 1. Métricas Importantes

- **Response Time**: < 2s para queries normais
- **Error Rate**: < 1% de erros
- **Database Connections**: < 80% do limite
- **Memory Usage**: < 512MB por função

### 2. Alertas Recomendados

\`\`\`bash
# Configurar no Vercel Dashboard
# - High error rate (> 5%)
# - Slow response time (> 5s)
# - High memory usage (> 400MB)
# - Database connection errors
\`\`\`

### 3. Backup e Recovery

\`\`\`bash
# Backup automático (configurado)
# Executado diariamente às 2:00 UTC

# Backup manual
curl -X POST https://your-app.vercel.app/api/database/backup

# Restore (se necessário)
# Contatar suporte do Neon para restore de backup
\`\`\`

## 🔄 CI/CD com GitHub Actions

### 1. Configurar Secrets no GitHub

\`\`\`bash
# Repository Settings -> Secrets and Variables -> Actions
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id
\`\`\`

### 2. Workflow Automático

\`\`\`yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
\`\`\`

## 🔐 Segurança em Produção

### 1. Configurações Obrigatórias

- ✅ HTTPS habilitado (automático no Vercel)
- ✅ Headers de segurança configurados
- ✅ Rate limiting ativo
- ✅ Validação de entrada
- ✅ Criptografia de dados sensíveis

### 2. Auditoria Regular

\`\`\`bash
# Verificar logs de segurança
curl https://your-app.vercel.app/api/audit/logs

# Analisar tentativas de acesso
vercel logs --filter="error"

# Verificar integridade dos dados
npm run db:integrity-check
\`\`\`

## 📈 Otimização de Performance

### 1. Database Optimization

\`\`\`sql
-- Índices recomendados (já criados automaticamente)
CREATE INDEX idx_mapas_mentais_nome ON mapas_mentais(nome);
CREATE INDEX idx_mapas_mentais_criado_em ON mapas_mentais(criado_em);
\`\`\`

### 2. Caching Strategy

- **Static Assets**: Cache automático do Vercel
- **API Responses**: Cache de 5 minutos para dados estáticos
- **Database Queries**: Connection pooling ativo

### 3. Monitoring Tools

- **Vercel Analytics**: Habilitado automaticamente
- **Database Metrics**: Endpoint `/api/metrics`
- **Custom Logging**: Estruturado em JSON

## 🆘 Suporte e Manutenção

### Contatos de Emergência
- **Vercel Support**: https://vercel.com/help
- **Neon Support**: https://neon.tech/docs/support
- **GitHub Issues**: Para bugs da aplicação

### Manutenção Regular
- **Weekly**: Verificar logs e métricas
- **Monthly**: Analisar performance e otimizar
- **Quarterly**: Atualizar dependências e security patches

---

## ✅ Checklist de Deploy

- [ ] Variáveis de ambiente configuradas
- [ ] Banco de dados Neon criado e conectado
- [ ] Testes passando localmente
- [ ] Build sem erros
- [ ] Deploy realizado com sucesso
- [ ] Health checks funcionando
- [ ] Monitoramento ativo
- [ ] Backup configurado
- [ ] Documentação atualizada

**🎉 Parabéns! Sua aplicação está no ar!**
\`\`\`

Agora vamos corrigir os problemas específicos que estão causando o erro no Vercel:

\`\`\`typescriptreact file="lib/env.ts"
[v0-no-op-code-block-prefix]import { z } from "zod"

// Schema de validação das variáveis de ambiente
const envSchema = z.object({
  // Database - obrigatório
  NEON_DATABASE_URL: z.string().min(1, "NEON_DATABASE_URL é obrigatória"),
  
  // Vercel - com defaults seguros
  VERCEL_REGION: z.string().default("us-east-1"),
  VERCEL_URL: z.string().optional(),
  
  // Performance - com defaults
  MAX_CONNECTIONS: z.string().transform(val => parseInt(val) || 10).default("10"),
  CONNECTION_TIMEOUT: z.string().transform(val => parseInt(val) || 30000).default("30000"),
  QUERY_TIMEOUT: z.string().transform(val => parseInt(val) || 10000).default("10000"),

  // Security - gerar defaults se não fornecidos
  SESSION_SECRET: z.string().default(() => require('crypto').randomBytes(32).toString('hex')),
  ENCRYPTION_KEY: z.string().default(() => require('crypto').randomBytes(32).toString('hex')),

  // AI Providers - opcionais
  OPENROUTER_API_KEY: z.string().optional(),
  OLLAMA_BASE_URL: z.string().url().default("http://localhost:11434"),

  // Application
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  LOG_LEVEL: z.enum(["debug", "info", "warn", "error"]).default("info"),

  // Features - defaults seguros
  METRICS_ENABLED: z.string().transform(val => val === "true").default("true"),
  BACKUP_ENABLED: z.string().transform(val => val === "true").default("true"),
  RATE_LIMIT_ENABLED: z.string().transform(val => val === "true").default("true"),
})

// Validar e exportar variáveis com tratamento de erro mais robusto
function validateEnv() {
  try {
    const env = envSchema.parse(process.env)
    return env
  } catch (error) {
    console.error("❌ Erro na validação das variáveis de ambiente:")

    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join(".")}: ${err.message}`)
      })
    }

    // Em desenvolvimento, continuar com defaults
    if (process.env.NODE_ENV === "development") {
      console.warn("⚠️ Usando valores padrão para desenvolvimento")
      return envSchema.parse({
        ...process.env,
        NEON_DATABASE_URL: process.env.NEON_DATABASE_URL || "postgresql://localhost:5432/mindmap",
      })
    }

    throw error
  }
}

export const env = validateEnv()

// Função para verificar se todas as variáveis críticas estão configuradas
export function checkCriticalEnvVars(): { valid: boolean; missing: string[]; warnings: string[] } {
  const missing: string[] = []
  const warnings: string[] = []

  // Variáveis obrigatórias
  if (!env.NEON_DATABASE_URL) missing.push("NEON_DATABASE_URL")
  if (!env.SESSION_SECRET) missing.push("SESSION_SECRET")
  if (!env.ENCRYPTION_KEY) missing.push("ENCRYPTION_KEY")

  // Variáveis recomendadas
  if (!env.OPENROUTER_API_KEY) warnings.push("OPENROUTER_API_KEY não configurada - funcionalidade de IA limitada")
  if (env.NODE_ENV === "production" && !env.VERCEL_URL) warnings.push("VERCEL_URL não detectada em produção")

  return {
    valid: missing.length === 0,
    missing,
    warnings,
  }
}

// Utilitário para logs seguros (sem expor secrets)
export function logEnvStatus() {
  const status = checkCriticalEnvVars()

  console.log("🔍 Status das Variáveis de Ambiente:")
  console.log(`  Environment: ${env.NODE_ENV}`)
  console.log(`  Region: ${env.VERCEL_REGION}`)
  console.log(`  Max Connections: ${env.MAX_CONNECTIONS}`)
  console.log(`  Database: ${env.NEON_DATABASE_URL ? "✅ Configurado" : "❌ Não configurado"}`)
  console.log(`  OpenRouter: ${env.OPENROUTER_API_KEY ? "✅ Configurado" : "⚠️ Não configurado"}`)

  if (status.warnings.length > 0) {
    console.warn("⚠️ Avisos:")
    status.warnings.forEach((warning) => console.warn(`  - ${warning}`))
  }

  if (!status.valid) {
    console.error("❌ Variáveis obrigatórias não configuradas:")
    status.missing.forEach((missing) => console.error(`  - ${missing}`))
  }
}

#!/usr/bin/env tsx

import { testDatabaseConnection, getSqlConnection, executeWithResilience } from "../lib/neon-connection"
import { configRepo, mindMapRepo } from "../lib/database-operations"
import { generateId } from "../lib/utils"

async function runDatabaseTests() {
  console.log("🧪 Iniciando testes do banco de dados...\n")

  let testsPassed = 0
  let testsTotal = 0

  function runTest(name: string, testFn: () => Promise<void>) {
    return async () => {
      testsTotal++
      console.log(`🔍 Teste: ${name}`)

      try {
        await testFn()
        testsPassed++
        console.log(`✅ ${name} - PASSOU\n`)
      } catch (error) {
        console.error(`❌ ${name} - FALHOU`)
        console.error(`   Erro: ${error instanceof Error ? error.message : error}\n`)
      }
    }
  }

  // Teste 1: Conexão básica
  await runTest("Conexão básica com Neon", async () => {
    const result = await testDatabaseConnection()
    if (!result.success) {
      throw new Error(result.message)
    }
  })()

  // Teste 2: Query simples
  await runTest("Query SQL simples", async () => {
    const sql = await getSqlConnection("test")
    const result = await sql`SELECT 1 as test_value`

    if (result[0].test_value !== 1) {
      throw new Error("Query não retornou valor esperado")
    }
  })()

  // Teste 3: Operações de configuração
  await runTest("CRUD de configurações", async () => {
    const testKey = `test_config_${Date.now()}`
    const testValue = "test_value"

    // Create
    await configRepo.set(testKey, testValue, "Teste de configuração")

    // Read
    const config = await configRepo.get(testKey)
    if (!config || config.valor !== testValue) {
      throw new Error("Falha ao ler configuração")
    }

    // Update
    const newValue = "updated_value"
    await configRepo.set(testKey, newValue)

    const updatedConfig = await configRepo.get(testKey)
    if (!updatedConfig || updatedConfig.valor !== newValue) {
      throw new Error("Falha ao atualizar configuração")
    }

    // Delete
    const deleted = await configRepo.delete(testKey)
    if (!deleted) {
      throw new Error("Falha ao deletar configuração")
    }

    // Verify deletion
    const deletedConfig = await configRepo.get(testKey)
    if (deletedConfig) {
      throw new Error("Configuração não foi deletada")
    }
  })()

  // Teste 4: Operações de mapa mental
  await runTest("CRUD de mapas mentais", async () => {
    const testMapData = {
      nome: `Teste Mapa ${Date.now()}`,
      descricao: "Mapa de teste",
      nos: [
        {
          id: generateId(),
          content: "Nó de teste",
          x: 100,
          y: 100,
          color: "#3b82f6",
          size: "medium" as const,
        },
      ],
      conexoes: [],
    }

    // Create
    const createdMap = await mindMapRepo.create(testMapData)
    if (!createdMap || createdMap.nome !== testMapData.nome) {
      throw new Error("Falha ao criar mapa mental")
    }

    // Read
    const retrievedMap = await mindMapRepo.getById(createdMap.id)
    if (!retrievedMap || retrievedMap.id !== createdMap.id) {
      throw new Error("Falha ao ler mapa mental")
    }

    // Update
    const updatedName = `Mapa Atualizado ${Date.now()}`
    const updatedMap = await mindMapRepo.update(createdMap.id, { nome: updatedName })
    if (!updatedMap || updatedMap.nome !== updatedName) {
      throw new Error("Falha ao atualizar mapa mental")
    }

    // Delete
    const deleted = await mindMapRepo.delete(createdMap.id)
    if (!deleted) {
      throw new Error("Falha ao deletar mapa mental")
    }

    // Verify deletion
    try {
      await mindMapRepo.getById(createdMap.id)
      throw new Error("Mapa mental não foi deletado")
    } catch (error) {
      // Esperado - mapa não deve existir
    }
  })()

  // Teste 5: Teste de performance
  await runTest("Performance de queries", async () => {
    const startTime = Date.now()

    // Executar múltiplas queries em paralelo
    const promises = Array.from({ length: 10 }, async (_, i) => {
      const sql = await getSqlConnection(`perf-test-${i}`)
      return sql`SELECT ${i} as test_number, NOW() as timestamp`
    })

    await Promise.all(promises)

    const duration = Date.now() - startTime
    console.log(`   Tempo para 10 queries paralelas: ${duration}ms`)

    if (duration > 5000) {
      throw new Error("Performance muito baixa - queries levaram mais de 5 segundos")
    }
  })()

  // Teste 6: Teste de resiliência
  await runTest("Resiliência com retry", async () => {
    let attempts = 0

    await executeWithResilience(
      async () => {
        attempts++
        if (attempts < 2) {
          throw new Error("Falha simulada")
        }

        const sql = await getSqlConnection("resilience-test")
        return sql`SELECT 'success' as result`
      },
      { maxRetries: 3, baseDelay: 100 },
    )

    if (attempts !== 2) {
      throw new Error(`Retry não funcionou corretamente - tentativas: ${attempts}`)
    }
  })()

  // Teste 7: Teste de transação
  await runTest("Transações do banco", async () => {
    const sql = await getSqlConnection("transaction-test")

    try {
      await sql.begin(async (tx) => {
        // Inserir dados de teste
        await tx`
          INSERT INTO configuracoes (chave, valor) 
          VALUES ('tx_test_1', 'value1'), ('tx_test_2', 'value2')
        `

        // Verificar se foram inseridos
        const result = await tx`
          SELECT COUNT(*) as count 
          FROM configuracoes 
          WHERE chave IN ('tx_test_1', 'tx_test_2')
        `

        if (result[0].count !== 2) {
          throw new Error("Dados não foram inseridos na transação")
        }

        // Simular erro para rollback
        throw new Error("Rollback intencional")
      })
    } catch (error) {
      // Esperado - rollback deve acontecer
    }

    // Verificar se rollback funcionou
    const finalResult = await sql`
      SELECT COUNT(*) as count 
      FROM configuracoes 
      WHERE chave IN ('tx_test_1', 'tx_test_2')
    `

    if (finalResult[0].count !== 0) {
      throw new Error("Rollback não funcionou - dados ainda existem")
    }
  })()

  // Teste 8: Estatísticas do banco
  await runTest("Estatísticas do sistema", async () => {
    const stats = await mindMapRepo.getStats()

    if (typeof stats.total !== "number" || stats.total < 0) {
      throw new Error("Estatísticas inválidas")
    }

    console.log(`   Total de mapas: ${stats.total}`)
    console.log(`   Mapas protegidos: ${stats.protected}`)
    console.log(`   Mapas recentes: ${stats.recent}`)
  })()

  // Resultado final
  console.log("📊 Resultado dos Testes:")
  console.log(`   Testes executados: ${testsTotal}`)
  console.log(`   Testes aprovados: ${testsPassed}`)
  console.log(`   Taxa de sucesso: ${((testsPassed / testsTotal) * 100).toFixed(1)}%`)

  if (testsPassed === testsTotal) {
    console.log("\n🎉 Todos os testes passaram! O banco de dados está funcionando corretamente.")
    process.exit(0)
  } else {
    console.log(`\n💥 ${testsTotal - testsPassed} teste(s) falharam. Verifique os problemas acima.`)
    process.exit(1)
  }
}

// Executar testes
runDatabaseTests().catch((error) => {
  console.error("💥 Erro fatal nos testes:", error)
  process.exit(1)
})

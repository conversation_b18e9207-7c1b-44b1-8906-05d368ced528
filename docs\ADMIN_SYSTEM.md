# 👑 Sistema de Administrador - Mindscape

O Mindscape agora inclui um sistema de administrador que restringe o acesso às configurações da aplicação apenas ao usuário administrador designado.

## 🚀 Funcionalidades

### ✅ **Controle de Acesso Administrativo**
- **Usuário Admin Único**: Apenas `<EMAIL>` tem privilégios de administrador
- **Configurações Protegidas**: Somente admin pode alterar configurações da aplicação
- **Interface Adaptativa**: Botão de configurações aparece apenas para admin
- **Segurança JWT**: Status de admin incluído no token de autenticação

### ✅ **Funcionalidades Administrativas**
- **Configuração de IA**: Alterar provedores e chaves de API
- **Configurações Gerais**: Modificar configurações da aplicação
- **Controle Total**: Acesso completo a todas as configurações do sistema
- **Validação Automática**: Sistema verifica automaticamente privilégios

## 🔧 Como Funciona

### **1. Identificação do Administrador**
- **Email específico**: `<EMAIL>` é automaticamente marcado como admin
- **Criação automática**: Status de admin definido durante registro
- **Verificação contínua**: Cada requisição valida privilégios de admin

### **2. Controle de Interface**
- **Botão de Configurações**: Visível apenas para admin
- **Acesso Negado**: Usuários comuns não veem opções administrativas
- **Feedback Visual**: Interface adapta-se automaticamente ao status do usuário

### **3. Proteção de APIs**
- **Middleware de Verificação**: Todas as APIs administrativas verificam privilégios
- **Erro 403**: Usuários não-admin recebem "Acesso Negado"
- **Logs de Segurança**: Tentativas de acesso não autorizado são registradas

## 🛠️ Arquitetura Técnica

### **Banco de Dados**
- **Campo is_admin** adicionado ao User:
```typescript
interface User {
  id: string
  email: string
  name: string
  password_hash: string
  is_admin?: boolean  // ← Novo campo
  created_at: string
  updated_at: string
}
```

### **Autenticação JWT**
- **Payload estendido** com informações de admin:
```typescript
interface JWTPayload {
  userId: string
  email: string
  name: string
  isAdmin?: boolean  // ← Incluído no token
}
```

### **Middleware de Segurança**
```typescript
export function requireAdmin(request: NextRequest): boolean {
  const user = getUserFromRequest(request)
  return user?.isAdmin === true
}
```

### **Proteção de APIs**
```typescript
// Exemplo: API de configuração protegida
export async function POST(request: NextRequest) {
  const user = getUserFromRequest(request)
  if (!user || !user.isAdmin) {
    return NextResponse.json(
      { error: "Acesso negado. Apenas administradores podem alterar configurações." },
      { status: 403 }
    )
  }
  // ... lógica da API
}
```

## 🎯 Fluxos de Uso

### **Fluxo de Administrador**
1. **Admin faz login** com `<EMAIL>`
2. **Sistema identifica** como administrador automaticamente
3. **Token JWT** inclui `isAdmin: true`
4. **Interface mostra** botão de configurações
5. **Admin acessa** configurações normalmente

### **Fluxo de Usuário Comum**
1. **Usuário faz login** com email comum
2. **Sistema identifica** como usuário regular
3. **Token JWT** não inclui privilégios de admin
4. **Interface oculta** botão de configurações
5. **Tentativas de acesso** retornam erro 403

### **Fluxo de Segurança**
1. **Requisição** para API administrativa
2. **Middleware verifica** token JWT
3. **Sistema valida** campo `isAdmin`
4. **Acesso liberado** ou **negado** conforme privilégios
5. **Logs registram** tentativas de acesso

## 📊 Interface do Usuário

### **Para Administrador**
```tsx
// Botão de configurações visível
{user?.isAdmin && (
  <Button onClick={() => setIsConfigModalOpen(true)}>
    <Settings className="h-4 w-4 mr-2" />
    Configurações
  </Button>
)}
```

### **Para Usuário Comum**
- **Botão oculto**: Configurações não aparecem na interface
- **Acesso direto bloqueado**: URLs de configuração retornam erro
- **Experiência limpa**: Interface sem opções inacessíveis

## 🔒 Segurança Implementada

### **Verificações de Segurança**
- **Email específico**: Apenas `<EMAIL>` pode ser admin
- **Verificação dupla**: Email + campo `is_admin` no banco
- **Token seguro**: JWT inclui status de admin criptografado
- **Middleware robusto**: Todas as rotas administrativas protegidas

### **Proteção contra Ataques**
- **Privilege Escalation**: Impossível elevar privilégios via API
- **Token Tampering**: JWT assinado e verificado
- **Direct Access**: URLs administrativas protegidas
- **Session Hijacking**: Cookies HTTP-only e seguros

### **Auditoria e Logs**
```typescript
// Logs de tentativas de acesso não autorizado
console.error('Unauthorized admin access attempt:', {
  userId: user?.userId,
  email: user?.email,
  endpoint: request.url,
  timestamp: new Date().toISOString()
})
```

## 🔄 Compatibilidade

### **Backward Compatibility**
- ✅ **Usuários existentes** continuam funcionando normalmente
- ✅ **Funcionalidades antigas** inalteradas para usuários comuns
- ✅ **Migração suave**: Campo `is_admin` opcional
- ✅ **Configurações existentes** mantidas

### **Integração com Outros Sistemas**
- **Autenticação**: Funciona com sistema de login existente
- **Compartilhamento**: Não afeta compartilhamento entre usuários
- **Mapas Mentais**: Funcionalidades de mapas inalteradas
- **Proteção por Senha**: Sistemas independentes

## 🚀 Deploy e Produção

### **Configuração do Administrador**
1. **Primeiro acesso**: Registre-se com `<EMAIL>`
2. **Status automático**: Sistema marca como admin automaticamente
3. **Verificação**: Faça login e confirme acesso às configurações
4. **Backup**: Mantenha backup do arquivo `users.json`

### **Segurança em Produção**
- **JWT_SECRET forte**: Use chave secreta robusta
- **HTTPS obrigatório**: Proteja tokens em trânsito
- **Logs de auditoria**: Monitore tentativas de acesso
- **Backup regular**: Proteja dados de usuários

## 🧪 Testando o Sistema

### **Teste de Acesso Administrativo**
```bash
# 1. Login como admin
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# 2. Acessar configurações (deve funcionar)
curl -X POST http://localhost:3000/api/config \
  -H "Content-Type: application/json" \
  -H "Cookie: auth-token=ADMIN_TOKEN" \
  -d '{"aiProvider":"openai"}'
```

### **Teste de Acesso Negado**
```bash
# 1. Login como usuário comum
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# 2. Tentar acessar configurações (deve falhar com 403)
curl -X POST http://localhost:3000/api/config \
  -H "Content-Type: application/json" \
  -H "Cookie: auth-token=USER_TOKEN" \
  -d '{"aiProvider":"openai"}'
```

### **Cenários de Teste**
1. **✅ Admin acessa configurações** - Deve funcionar
2. **❌ Usuário comum acessa configurações** - Deve falhar (403)
3. **✅ Admin vê botão de configurações** - Interface adaptada
4. **❌ Usuário comum não vê botão** - Interface limpa
5. **✅ Registro automático de admin** - Email específico vira admin
6. **❌ Tentativa de elevar privilégios** - Deve falhar

## 🎯 Benefícios do Sistema

### **Para Administradores**
- **Controle total**: Acesso completo às configurações
- **Segurança**: Proteção contra alterações não autorizadas
- **Simplicidade**: Interface clara e direta
- **Auditoria**: Logs de todas as ações administrativas

### **Para Usuários**
- **Interface limpa**: Sem opções inacessíveis
- **Experiência focada**: Apenas funcionalidades relevantes
- **Segurança**: Proteção contra alterações acidentais
- **Performance**: Interface otimizada sem elementos desnecessários

### **Para o Sistema**
- **Segurança robusta**: Múltiplas camadas de proteção
- **Escalabilidade**: Fácil adição de novos recursos administrativos
- **Manutenibilidade**: Código limpo e bem estruturado
- **Auditoria**: Rastreamento completo de ações administrativas

---

**👑 O sistema de administrador está totalmente funcional e seguro!**

### **🔑 Credenciais do Administrador**
- **Email**: `<EMAIL>`
- **Senha**: Definida durante o registro
- **Privilégios**: Acesso total às configurações da aplicação

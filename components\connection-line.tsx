"use client"

interface ConnectionLineProps {
  from: { x: number; y: number }
  to: { x: number; y: number }
  type: "semantic" | "manual"
  strength: number
}

export function ConnectionLine({ from, to, type, strength }: ConnectionLineProps) {
  // Calcular pontos de controle para linha curva
  const dx = to.x - from.x
  const dy = to.y - from.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  // Calcular pontos de início e fim na borda dos nós
  const nodeRadius = 60; // raio aproximado do nó
  const fromAngle = Math.atan2(dy, dx);
  const toAngle = Math.atan2(-dy, -dx);

  const fromX = from.x + Math.cos(fromAngle) * nodeRadius;
  const fromY = from.y + Math.sin(fromAngle) * nodeRadius;
  const toX = to.x + Math.cos(toAngle) * nodeRadius;
  const toY = to.y + Math.sin(toAngle) * nodeRadius;

  const adjustedDx = toX - fromX;
  const adjustedDy = toY - fromY;
  const adjustedDistance = Math.sqrt(adjustedDx * adjustedDx + adjustedDy * adjustedDy);

  // Criar um caminho curvo
  const midX = (fromX + toX) / 2
  const midY = (fromY + toY) / 2

  // Deslocar o ponto de controle perpendicular à linha
  const offsetX = (-adjustedDy / adjustedDistance) * 30
  const offsetY = (adjustedDx / adjustedDistance) * 30

  const controlX = midX + offsetX
  const controlY = midY + offsetY

  const pathData = `M ${fromX} ${fromY} Q ${controlX} ${controlY} ${toX} ${toY}`

  return (
    <g>
      {/* Linha de conexão */}
      <path
        d={pathData}
        fill="none"
        stroke={type === "semantic" ? "#10b981" : "#6b7280"}
        strokeWidth={Math.max(1, strength * 3)}
        strokeDasharray={type === "semantic" ? "5,5" : "none"}
        opacity={0.7}
        className="pointer-events-none"
      />

      {/* Cabeça da seta */}
      <defs>
        <marker id={`arrowhead-${type}`} markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill={type === "semantic" ? "#10b981" : "#6b7280"} opacity={0.7} />
        </marker>
      </defs>

      <path d={pathData} fill="none" stroke="transparent" strokeWidth="1" markerEnd={`url(#arrowhead-${type})`} />
    </g>
  )
}

# 🔐 Sistema de Autenticação - Mindscape

O Mindscape agora inclui um sistema completo de autenticação de usuários que permite controle individual sobre mapas mentais.

## 🚀 Funcionalidades

### ✅ **Autenticação Completa**
- **Registro de usuários** com validação de email e senha
- **Login seguro** com verificação de credenciais
- **Logout** com limpeza de sessão
- **Sessões persistentes** com JWT e cookies HTTP-only

### ✅ **Segurança**
- **Senhas criptografadas** com bcrypt (salt rounds: 12)
- **Tokens JWT** com expiração de 7 dias
- **Cookies HTTP-only** para prevenir ataques XSS
- **Validação de entrada** com Zod schemas

### ✅ **Controle de Mapas Mentais**
- **Mapas por usuário**: Cada usuário vê apenas seus próprios mapas
- **Compatibilidade**: Mapas existentes continuam funcionando
- **Modo anônimo**: Funciona sem login (mapas locais)

## 🔧 Como Usar

### **1. Registro de Novo Usuário**
1. Clique em "Entrar" no header
2. Clique em "Criar conta"
3. Preencha: Nome, Email, Senha (mín. 6 caracteres)
4. Confirme a senha
5. Clique em "Criar Conta"

### **2. Login**
1. Clique em "Entrar" no header
2. Digite seu email e senha
3. Clique em "Entrar"

### **3. Logout**
1. Clique em "Sair" no header (quando logado)

## 🛠️ Arquitetura Técnica

### **APIs de Autenticação**
```
POST /api/auth/register  - Registro de usuário
POST /api/auth/login     - Login de usuário  
POST /api/auth/logout    - Logout de usuário
GET  /api/auth/me        - Dados do usuário atual
```

### **Banco de Dados**
- **Arquivo**: `data/users.json`
- **Estrutura**:
```json
{
  "id": "uuid",
  "email": "<EMAIL>", 
  "name": "Nome do Usuário",
  "password_hash": "$2b$12$...",
  "created_at": "2025-01-01T00:00:00.000Z",
  "updated_at": "2025-01-01T00:00:00.000Z"
}
```

### **Mapas Mentais com Usuários**
- **Campo adicionado**: `user_id` em cada mapa mental
- **Filtro automático**: APIs filtram por usuário logado
- **Compatibilidade**: Mapas sem `user_id` são tratados como anônimos

### **Componentes React**
- `AuthProvider` - Context provider para estado de autenticação
- `AuthModal` - Modal principal de login/registro
- `LoginForm` - Formulário de login
- `RegisterForm` - Formulário de registro
- `useAuth` - Hook para gerenciar estado de autenticação

## 🔒 Segurança Implementada

### **Proteção de Senhas**
```typescript
// Criptografia com bcrypt
const hashedPassword = await bcrypt.hash(password, 12)
const isValid = await bcrypt.compare(password, user.password_hash)
```

### **JWT Tokens**
```typescript
// Geração de token
const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })

// Verificação de token
const decoded = jwt.verify(token, JWT_SECRET)
```

### **Cookies Seguros**
```typescript
response.cookies.set('auth-token', token, {
  httpOnly: true,                    // Previne acesso via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS em produção
  sameSite: 'lax',                  // Proteção CSRF
  maxAge: 60 * 60 * 24 * 7         // 7 dias
})
```

## 🔄 Compatibilidade

### **Modo Híbrido**
- **Com login**: Mapas são associados ao usuário
- **Sem login**: Mapas são salvos localmente (como antes)
- **Migração**: Usuários podem fazer login e continuar usando mapas existentes

### **Backward Compatibility**
- ✅ Mapas existentes continuam funcionando
- ✅ Configurações mantidas
- ✅ Funcionalidades existentes inalteradas
- ✅ Pode ser usado sem autenticação

## 🚀 Deploy e Produção

### **Variáveis de Ambiente**
```env
# JWT Secret (OBRIGATÓRIO em produção)
JWT_SECRET=your-super-secret-key-change-this-in-production

# Database (opcional - usa JSON local por padrão)
DATABASE_URL=postgresql://...
```

### **Configuração de Produção**
1. **Defina JWT_SECRET** forte e único
2. **Configure HTTPS** para cookies seguros
3. **Backup regular** do arquivo `data/users.json`
4. **Monitore logs** de autenticação

## 📊 Monitoramento

### **Logs de Autenticação**
- Tentativas de login (sucesso/falha)
- Registros de novos usuários
- Verificações de token
- Erros de autenticação

### **Métricas Importantes**
- Número de usuários registrados
- Taxa de sucesso de login
- Mapas mentais por usuário
- Sessões ativas

## 🔧 Desenvolvimento

### **Testando APIs**
```bash
# Registro
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Teste","email":"<EMAIL>","password":"123456"}'

# Login  
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# Verificar usuário atual
curl http://localhost:3000/api/auth/me \
  -H "Cookie: auth-token=YOUR_TOKEN"
```

### **Estrutura de Arquivos**
```
app/api/auth/
├── register/route.ts    # Registro de usuário
├── login/route.ts       # Login de usuário
├── logout/route.ts      # Logout de usuário
└── me/route.ts          # Dados do usuário atual

components/auth/
├── auth-provider.tsx    # Context provider
├── auth-modal.tsx       # Modal principal
├── login-form.tsx       # Formulário de login
└── register-form.tsx    # Formulário de registro

lib/
├── auth.ts              # Utilitários JWT
└── middleware.ts        # Middleware de autenticação

hooks/
└── use-auth.ts          # Hook de autenticação
```

---

**🎉 O sistema de autenticação está totalmente funcional e pronto para uso!**

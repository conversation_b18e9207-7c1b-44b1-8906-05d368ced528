import { pgTable, text, timestamp, boolean, jsonb, integer, index, uniqueIndex } from "drizzle-orm/pg-core"
import { relations } from "drizzle-orm"

// Tabela de configurações do sistema
export const configuracoes = pgTable(
  "configuracoes",
  {
    id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
    chave: text("chave").notNull().unique(),
    valor: text("valor").notNull(),
    descricao: text("descricao"),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
    atualizadoEm: timestamp("atualizado_em").defaultNow().notNull(),
  },
  (table) => ({
    chaveIdx: uniqueIndex("configuracoes_chave_idx").on(table.chave),
    atualizadoEmIdx: index("configuracoes_atualizado_em_idx").on(table.atualizadoEm),
  }),
)

// Tabela de mapas mentais
export const mapasMentais = pgTable(
  "mapas_mentais",
  {
    id: text("id").primaryKey(),
    nome: text("nome").notNull(),
    descricao: text("descricao"),
    nos: jsonb("nos").notNull(),
    conexoes: jsonb("conexoes").notNull(),
    protegidoSenha: boolean("protegido_senha").default(false).notNull(),
    hashSenha: text("hash_senha"),
    tags: jsonb("tags").default([]),
    metadados: jsonb("metadados").default({}),
    versao: integer("versao").default(1).notNull(),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
    atualizadoEm: timestamp("atualizado_em").defaultNow().notNull(),
    acessadoEm: timestamp("acessado_em").defaultNow(),
  },
  (table) => ({
    nomeIdx: index("mapas_mentais_nome_idx").on(table.nome),
    criadoEmIdx: index("mapas_mentais_criado_em_idx").on(table.criadoEm),
    atualizadoEmIdx: index("mapas_mentais_atualizado_em_idx").on(table.atualizadoEm),
    protegidoSenhaIdx: index("mapas_mentais_protegido_senha_idx").on(table.protegidoSenha),
    tagsIdx: index("mapas_mentais_tags_idx").using("gin", table.tags),
  }),
)

// Tabela de sessões de usuário (para futuro uso)
export const sessoes = pgTable(
  "sessoes",
  {
    id: text("id").primaryKey(),
    dadosSessao: jsonb("dados_sessao").notNull(),
    expiresEm: timestamp("expires_em").notNull(),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
  },
  (table) => ({
    expiresEmIdx: index("sessoes_expires_em_idx").on(table.expiresEm),
  }),
)

// Tabela de logs de auditoria
export const logsAuditoria = pgTable(
  "logs_auditoria",
  {
    id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
    acao: text("acao").notNull(),
    recurso: text("recurso").notNull(),
    recursoId: text("recurso_id"),
    dadosAntigos: jsonb("dados_antigos"),
    dadosNovos: jsonb("dados_novos"),
    metadados: jsonb("metadados").default({}),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
  },
  (table) => ({
    acaoIdx: index("logs_auditoria_acao_idx").on(table.acao),
    recursoIdx: index("logs_auditoria_recurso_idx").on(table.recurso),
    criadoEmIdx: index("logs_auditoria_criado_em_idx").on(table.criadoEm),
  }),
)

// Tabela de backups
export const backups = pgTable(
  "backups",
  {
    id: text("id").primaryKey(),
    tipo: text("tipo").notNull(),
    dados: jsonb("dados").notNull(),
    tamanho: integer("tamanho"),
    checksum: text("checksum"),
    metadados: jsonb("metadados").default({}),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
  },
  (table) => ({
    tipoIdx: index("backups_tipo_idx").on(table.tipo),
    criadoEmIdx: index("backups_criado_em_idx").on(table.criadoEm),
  }),
)

// Tabela de métricas de performance
export const metricas = pgTable(
  "metricas",
  {
    id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
    operacao: text("operacao").notNull(),
    tempoExecucao: integer("tempo_execucao").notNull(),
    sucesso: boolean("sucesso").notNull(),
    erro: text("erro"),
    metadados: jsonb("metadados").default({}),
    criadoEm: timestamp("criado_em").defaultNow().notNull(),
  },
  (table) => ({
    operacaoIdx: index("metricas_operacao_idx").on(table.operacao),
    sucessoIdx: index("metricas_sucesso_idx").on(table.sucesso),
    criadoEmIdx: index("metricas_criado_em_idx").on(table.criadoEm),
  }),
)

// Definir relacionamentos (para futuro uso)
export const mapasMentaisRelations = relations(mapasMentais, ({ many }) => ({
  logs: many(logsAuditoria),
}))

export const logsAuditoriaRelations = relations(logsAuditoria, ({ one }) => ({
  mapaMental: one(mapasMentais, {
    fields: [logsAuditoria.recursoId],
    references: [mapasMentais.id],
  }),
}))

// Tipos TypeScript derivados do schema
export type ConfiguracaoInsert = typeof configuracoes.$inferInsert
export type ConfiguracaoSelect = typeof configuracoes.$inferSelect

export type MapaMentalInsert = typeof mapasMentais.$inferInsert
export type MapaMentalSelect = typeof mapasMentais.$inferSelect

export type SessaoInsert = typeof sessoes.$inferInsert
export type SessaoSelect = typeof sessoes.$inferSelect

export type LogAuditoriaInsert = typeof logsAuditoria.$inferInsert
export type LogAuditoriaSelect = typeof logsAuditoria.$inferSelect

export type BackupInsert = typeof backups.$inferInsert
export type BackupSelect = typeof backups.$inferSelect

export type MetricaInsert = typeof metricas.$inferInsert
export type MetricaSelect = typeof metricas.$inferSelect

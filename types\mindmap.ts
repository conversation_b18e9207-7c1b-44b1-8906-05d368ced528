export interface MindMapNode {
  id: string
  content: string
  x: number
  y: number
  level: number
  color?: string
  size?: "small" | "medium" | "large"
  icon?: string
  style?: "filled" | "outline"
}

export interface MindMapConnection {
  from: string
  to: string
  color?: string
  style?: "solid" | "dashed" | "dotted"
}

export interface MindMap {
  id?: string | number
  titulo: string
  dados: {
    nodes: MindMapNode[]
    connections: MindMapConnection[]
    settings?: {
      zoom: number
      centerX: number
      centerY: number
    }
  }
  criado_em?: string
  atualizado_em?: string
  user_id?: string
  is_protected?: boolean
  password_hash?: string
}

export interface User {
  id: string
  email: string
  name: string
  password_hash: string
  is_admin?: boolean
  created_at: string
  updated_at: string
}

export interface SharedMindMap {
  id: string
  mindmap_id: string
  owner_id: string
  shared_with_id: string
  shared_with_email: string
  permission: 'view' | 'edit'
  created_at: string
  updated_at: string
}

export interface SystemConfig {
  llm_provider: "openrouter" | "ollama"
  openrouter_api_key: string
  ollama_base_url: string
  modelo_padrao: string
  tema: "claro" | "escuro"
  zoom_padrao: number
  auto_salvar: boolean
  grade_visivel: boolean
}

export interface ChatMessage {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

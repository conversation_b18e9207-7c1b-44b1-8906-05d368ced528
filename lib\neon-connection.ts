import { neon } from "@neondatabase/serverless"
import { env } from "./env"

// Interface para conexão SQL
interface SqlConnection {
  query: (text: string, params?: any[]) => Promise<any[]>
  transaction: <T>(callback: (sql: SqlConnection) => Promise<T>) => Promise<T>
}

// Pool de conexões simples
class ConnectionPool {
  private connections: Map<string, any> = new Map()
  private maxConnections = env.MAX_CONNECTIONS
  private connectionTimeout = env.CONNECTION_TIMEOUT

  async getConnection(): Promise<any> {
    const connectionId = `conn_${Date.now()}_${Math.random()}`

    if (this.connections.size >= this.maxConnections) {
      throw new Error("Maximum connections reached")
    }

    try {
      const sql = neon(env.DATABASE_URL)
      this.connections.set(connectionId, sql)

      // Auto-cleanup após timeout
      setTimeout(() => {
        this.connections.delete(connectionId)
      }, this.connectionTimeout)

      return sql
    } catch (error) {
      console.error("Erro ao criar conexão:", error)
      throw error
    }
  }

  getActiveConnections(): number {
    return this.connections.size
  }

  closeAll(): void {
    this.connections.clear()
  }
}

// Instância do pool
const pool = new ConnectionPool()

// Função principal para obter conexão SQL
export async function getSqlConnection(): Promise<any> {
  try {
    return await pool.getConnection()
  } catch (error) {
    console.error("Erro ao obter conexão SQL:", error)
    throw error
  }
}

// Teste de conexão com o banco
export async function testDatabaseConnection(): Promise<{ success: boolean; message: string; details?: any }> {
  try {
    const sql = await getSqlConnection()
    const result = await sql`SELECT 1 as test`

    if (result && result[0]?.test === 1) {
      return {
        success: true,
        message: "Database connection successful",
        details: {
          activeConnections: pool.getActiveConnections(),
          maxConnections: env.MAX_CONNECTIONS,
          region: env.VERCEL_REGION,
        },
      }
    } else {
      return {
        success: false,
        message: "Database test query failed",
      }
    }
  } catch (error) {
    console.error("Erro no teste de conexão:", error)
    return {
      success: false,
      message: `Database connection failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}

// Execução com retry e resilência
export async function executeWithResilience<T>(
  operation: (sql: any) => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000,
): Promise<T> {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const sql = await getSqlConnection()
      return await operation(sql)
    } catch (error) {
      lastError = error instanceof Error ? error : new Error("Unknown error")

      if (attempt === maxRetries) {
        break
      }

      // Backoff exponencial
      const delay = baseDelay * Math.pow(2, attempt - 1)
      console.warn(`Tentativa ${attempt} falhou, tentando novamente em ${delay}ms:`, lastError.message)

      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  throw lastError || new Error("Operation failed after all retries")
}

// Utilitário para transações
export async function withTransaction<T>(callback: (sql: any) => Promise<T>): Promise<T> {
  return executeWithResilience(async (sql) => {
    // Neon não suporta transações explícitas da mesma forma que PostgreSQL tradicional
    // Mas podemos simular com try/catch
    try {
      return await callback(sql)
    } catch (error) {
      console.error("Erro na transação:", error)
      throw error
    }
  })
}

// Inicialização do banco de dados
export async function initializeDatabase(): Promise<boolean> {
  try {
    const sql = await getSqlConnection()

    // Criar tabelas se não existirem
    await sql`
      CREATE TABLE IF NOT EXISTS configuracoes (
        chave TEXT PRIMARY KEY,
        valor TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `

    await sql`
      CREATE TABLE IF NOT EXISTS mapas_mentais (
        id TEXT PRIMARY KEY,
        titulo TEXT NOT NULL,
        descricao TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `

    await sql`
      CREATE TABLE IF NOT EXISTS nos (
        id TEXT PRIMARY KEY,
        mapa_id TEXT NOT NULL,
        content TEXT NOT NULL,
        x REAL NOT NULL,
        y REAL NOT NULL,
        color TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (mapa_id) REFERENCES mapas_mentais(id) ON DELETE CASCADE
      )
    `

    await sql`
      CREATE TABLE IF NOT EXISTS conexoes (
        id TEXT PRIMARY KEY,
        mapa_id TEXT NOT NULL,
        from_node_id TEXT NOT NULL,
        to_node_id TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (mapa_id) REFERENCES mapas_mentais(id) ON DELETE CASCADE,
        FOREIGN KEY (from_node_id) REFERENCES nos(id) ON DELETE CASCADE,
        FOREIGN KEY (to_node_id) REFERENCES nos(id) ON DELETE CASCADE
      )
    `

    // Criar índices para performance
    await sql`CREATE INDEX IF NOT EXISTS idx_nos_mapa_id ON nos(mapa_id)`
    await sql`CREATE INDEX IF NOT EXISTS idx_conexoes_mapa_id ON conexoes(mapa_id)`
    await sql`CREATE INDEX IF NOT EXISTS idx_mapas_updated_at ON mapas_mentais(updated_at)`

    console.log("✅ Database initialized successfully")
    return true
  } catch (error) {
    console.error("❌ Failed to initialize database:", error)
    return false
  }
}

// Monitor de saúde da conexão
export const healthMonitor = {
  async check() {
    const startTime = Date.now()

    try {
      const result = await testDatabaseConnection()
      const responseTime = Date.now() - startTime

      return {
        ...result,
        responseTime,
        timestamp: new Date().toISOString(),
        pool: {
          active: pool.getActiveConnections(),
          max: env.MAX_CONNECTIONS,
        },
      }
    } catch (error) {
      return {
        success: false,
        message: `Health check failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      }
    }
  },

  async getMetrics() {
    return {
      activeConnections: pool.getActiveConnections(),
      maxConnections: env.MAX_CONNECTIONS,
      connectionTimeout: env.CONNECTION_TIMEOUT,
      queryTimeout: env.QUERY_TIMEOUT,
      region: env.VERCEL_REGION,
      timestamp: new Date().toISOString(),
    }
  },
}

// Cleanup ao encerrar processo
if (typeof process !== "undefined") {
  process.on("SIGTERM", () => {
    console.log("Closing database connections...")
    pool.closeAll()
  })

  process.on("SIGINT", () => {
    console.log("Closing database connections...")
    pool.closeAll()
  })
}

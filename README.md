# 🧠 Mindscape - Mapas Mentais com IA

<div align="center">

![Mindscape Logo](https://img.shields.io/badge/Mindscape-🧠-blue?style=for-the-badge)
[![Next.js](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)

**Uma aplicação web completa para criação, edição e compartilhamento de mapas mentais com inteligência artificial**

[🚀 Demo](#-demo) • [📖 Documentação](#-documentação) • [⚡ Início Rápido](#-início-rápido) • [🤝 Contribuição](#-contribuição)

</div>

---

## 📋 Índice

- [🌟 Visão Geral](#-visão-geral)
- [✨ Funcionalidades](#-funcionalidades)
- [🛠️ Tecnologias](#️-tecnologias)
- [⚡ Início Rápido](#-início-rápido)
- [🔧 Configuração](#-configuração)
- [🎯 Como Usar](#-como-usar)
- [🔐 Sistema de Autenticação](#-sistema-de-autenticação)
- [🤝 Sistema de Compartilhamento](#-sistema-de-compartilhamento)
- [📊 Banco de Dados](#-banco-de-dados)
- [🚀 Deploy](#-deploy)
- [📖 Documentação](#-documentação)
- [🧪 Testes](#-testes)
- [🤝 Contribuição](#-contribuição)
- [📄 Licença](#-licença)

---

## 🌟 Visão Geral

O **Mindscape** é uma aplicação web moderna e completa para criação, edição e compartilhamento de mapas mentais. Desenvolvida com Next.js 14 e TypeScript, oferece uma experiência intuitiva com recursos avançados de inteligência artificial para geração automática de conteúdo.

### 🎯 **Principais Diferenciais:**
- **🤖 IA Integrada**: Geração automática de mapas mentais com OpenAI
- **👥 Colaboração**: Sistema completo de compartilhamento entre usuários
- **🔐 Segurança**: Autenticação JWT com criptografia bcrypt
- **📱 Responsivo**: Interface adaptável para desktop e mobile
- **💾 Persistência**: Banco de dados JSON local com suporte a PostgreSQL
- **🎨 Interface Moderna**: Design clean com Tailwind CSS e componentes shadcn/ui

---

## ✨ Funcionalidades

### 🧠 **Mapas Mentais Inteligentes**
- ✅ **Criação Visual**: Interface drag-and-drop intuitiva
- ✅ **Geração por IA**: Criação automática baseada em prompts
- ✅ **Edição em Tempo Real**: Modificação instantânea de nós e conexões
- ✅ **Ícones Dinâmicos**: Geração automática de ícones para nós
- ✅ **Exportação**: PDF, Markdown e outros formatos

### 👤 **Sistema de Usuários**
- ✅ **Registro e Login**: Autenticação segura com JWT
- ✅ **Perfis de Usuário**: Gerenciamento de conta pessoal
- ✅ **Mapas Privados**: Cada usuário tem seus próprios mapas
- ✅ **Sessões Persistentes**: Login automático por 7 dias

### 🤝 **Colaboração e Compartilhamento**
- ✅ **Compartilhamento por Email**: Convide usuários por email
- ✅ **Permissões Granulares**: Visualizar ou Editar
- ✅ **Mapas Compartilhados**: Aba dedicada para mapas recebidos
- ✅ **Gerenciamento de Shares**: Controle total sobre compartilhamentos

### 🎨 **Interface e Experiência**
- ✅ **Design Responsivo**: Funciona em desktop, tablet e mobile
- ✅ **Tema Moderno**: Interface clean e profissional
- ✅ **Navegação Intuitiva**: Fluxos de uso otimizados
- ✅ **Feedback Visual**: Toasts, loading states e validações

### 🔧 **Recursos Técnicos**
- ✅ **Modo Offline**: Funciona sem conexão com internet
- ✅ **Backup Automático**: Salvamento contínuo de dados
- ✅ **Configuração Flexível**: Suporte a múltiplos provedores de IA
- ✅ **Escalabilidade**: Arquitetura preparada para crescimento

---

## 🛠️ Tecnologias

### **Frontend**
- **[Next.js 14](https://nextjs.org/)** - Framework React com App Router
- **[React 18](https://reactjs.org/)** - Biblioteca de interface de usuário
- **[TypeScript](https://www.typescriptlang.org/)** - Tipagem estática para JavaScript
- **[Tailwind CSS](https://tailwindcss.com/)** - Framework CSS utilitário
- **[shadcn/ui](https://ui.shadcn.com/)** - Componentes de interface modernos

### **Backend**
- **[Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)** - APIs serverless
- **[bcryptjs](https://github.com/dcodeIO/bcrypt.js)** - Criptografia de senhas
- **[jsonwebtoken](https://github.com/auth0/node-jsonwebtoken)** - Autenticação JWT
- **[zod](https://github.com/colinhacks/zod)** - Validação de esquemas

### **Inteligência Artificial**
- **[OpenAI API](https://openai.com/api/)** - Geração de conteúdo com GPT
- **[Anthropic Claude](https://www.anthropic.com/)** - Modelo alternativo de IA
- **Prompts Otimizados** - Templates especializados para mapas mentais

### **Banco de Dados**
- **JSON Local** - Armazenamento em arquivos para desenvolvimento
- **[Neon PostgreSQL](https://neon.tech/)** - Banco de dados em produção
- **Zustand** - Gerenciamento de estado global

### **Ferramentas de Desenvolvimento**
- **[ESLint](https://eslint.org/)** - Linting de código
- **[Prettier](https://prettier.io/)** - Formatação de código
- **[Git](https://git-scm.com/)** - Controle de versão

---

## ⚡ Início Rápido

### **1. Instalação**

```bash
# Clone o repositório
git clone https://github.com/leoaalvsufg/mindscape.git
cd mindscape

# Instale as dependências
pnpm install
# ou
npm install

# Execute em desenvolvimento
pnpm dev
# ou
npm run dev

# Acesse a aplicação
# http://localhost:3000
```

### **2. Primeira Configuração**

1. **Abra a aplicação** em `http://localhost:3000`
2. **Complete o Setup Wizard** na primeira execução
3. **Configure seu provedor de IA**:
   - OpenAI: Insira sua API key
   - Anthropic: Configure Claude (opcional)
4. **Crie sua conta** ou use sem autenticação
5. **Comece a criar** seus mapas mentais!

---

## 🔧 Configuração

### **Variáveis de Ambiente**

Crie um arquivo `.env.local` na raiz do projeto:

```env
# ===== OBRIGATÓRIAS =====
# Chave secreta para JWT (MUDE EM PRODUÇÃO!)
JWT_SECRET=your-super-secret-key-change-this-in-production

# ===== OPCIONAIS =====
# Diretório do banco de dados
DATA_DIR=./data

# Ambiente de execução
NODE_ENV=development

# Banco de dados PostgreSQL (produção)
DATABASE_URL=postgresql://user:password@host:port/database

# APIs de IA (configure na interface)
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
```

### **Configuração de IA**

#### **OpenAI (Recomendado)**
1. Acesse [platform.openai.com](https://platform.openai.com)
2. Crie uma conta e obtenha sua API key
3. Configure na interface: Configurações → IA → OpenAI

#### **Anthropic Claude**
1. Acesse [console.anthropic.com](https://console.anthropic.com)
2. Obtenha sua API key
3. Configure na interface: Configurações → IA → Anthropic

### **Configuração de Banco**

#### **Desenvolvimento (JSON Local)**
- Automático: Arquivos criados em `./data/`
- Backup: Copie os arquivos `.json`
- Reset: Delete a pasta `./data/`

#### **Produção (PostgreSQL)**
- Configure `DATABASE_URL` no `.env.local`
- Suporte automático ao Neon, Supabase, etc.
- Migração automática na primeira execução

---

## 🎯 Como Usar

### **1. 🚀 Criando seu Primeiro Mapa**

1. **Acesse a aplicação** e faça login (opcional)
2. **Digite um tópico** na caixa "Descreva o tópico do seu mapa mental"
3. **Clique em "Gerar Mapa Mental"** e aguarde a IA criar
4. **Edite o mapa** clicando em "Editar" no card gerado

### **2. ✏️ Editando Mapas Mentais**

#### **Interface do Editor**
- **Canvas Central**: Área de trabalho principal
- **Nós**: Caixas com conteúdo conectadas
- **Conexões**: Linhas ligando os nós
- **Toolbar**: Ferramentas de edição

#### **Ações Básicas**
- **Adicionar Nó**: Clique no botão "+" ou duplo-clique no canvas
- **Editar Nó**: Clique duplo no nó para editar texto
- **Mover Nó**: Arraste o nó para nova posição
- **Conectar Nós**: Arraste de um nó para outro
- **Deletar**: Selecione e pressione Delete

#### **Recursos Avançados**
- **Ícones**: Geração automática baseada no conteúdo
- **Níveis**: Hierarquia visual automática
- **Zoom**: Scroll do mouse ou botões +/-
- **Pan**: Arraste o canvas vazio

### **3. 💾 Salvando e Gerenciando**

#### **Salvamento**
- **Automático**: Salva a cada alteração
- **Manual**: Botão "Salvar" no editor
- **Backup**: Dados persistidos localmente

#### **Organização**
- **Lista de Mapas**: Visualize todos seus mapas
- **Busca**: Encontre mapas por título
- **Filtros**: Organize por data, tamanho, etc.
- **Exclusão**: Delete mapas não utilizados

---

## 🔐 Sistema de Autenticação

### **Funcionalidades de Usuário**

#### **Registro e Login**
- **Registro**: Nome, email e senha (mín. 6 caracteres)
- **Login**: Email e senha com sessão persistente
- **Logout**: Limpeza segura de sessão
- **Sessões**: JWT com duração de 7 dias

#### **Segurança**
- **Criptografia**: Senhas com bcrypt (12 rounds)
- **JWT**: Tokens seguros com expiração
- **Cookies**: HTTP-only para prevenir XSS
- **Validação**: Schemas Zod para entrada de dados

### **Como Usar Autenticação**

#### **1. Criar Conta**
1. Clique em **"Entrar"** no header
2. Clique em **"Criar conta"**
3. Preencha: Nome, Email, Senha
4. Confirme a senha
5. Clique em **"Criar Conta"**

#### **2. Fazer Login**
1. Clique em **"Entrar"** no header
2. Digite email e senha
3. Clique em **"Entrar"**
4. Sessão válida por 7 dias

#### **3. Modo Anônimo**
- **Funciona sem login** para uso local
- **Mapas salvos localmente** sem sincronização
- **Sem compartilhamento** entre dispositivos

---

## 🤝 Sistema de Compartilhamento

### **Colaboração Entre Usuários**

#### **Compartilhar Mapas**
- **Por Email**: Compartilhe com usuários cadastrados
- **Permissões**: Visualizar ou Visualizar + Editar
- **Gestão**: Controle total sobre compartilhamentos
- **Notificações**: Feedback visual de ações

#### **Permissões Disponíveis**
- **👁️ Visualizar**: Usuário pode apenas ver o mapa
- **✏️ Editar**: Usuário pode ver e modificar o mapa
- **🗑️ Remover**: Proprietário pode revogar acesso

### **Como Compartilhar**

#### **1. Compartilhar um Mapa**
1. Vá para **"Meus Mapas"**
2. Clique no ícone **🔗** no mapa desejado
3. Digite o **email do destinatário**
4. Escolha a **permissão** (Ver ou Editar)
5. Clique em **"Compartilhar"**

#### **2. Visualizar Mapas Compartilhados**
1. Clique na aba **"Mapas Compartilhados"**
2. Veja mapas compartilhados com você
3. Informações do proprietário exibidas
4. Permissões indicadas por badges

#### **3. Gerenciar Compartilhamentos**
1. Abra o modal de compartilhamento
2. Veja lista **"Compartilhado com:"**
3. Clique em **🗑️** para remover acesso

### **Interface de Compartilhamento**

#### **Abas de Navegação**
- **📄 Meus Mapas**: Mapas criados por você
- **👥 Mapas Compartilhados**: Mapas compartilhados com você

#### **Indicadores Visuais**
- **Badge de Permissão**: Ver/Editar
- **Nome do Proprietário**: "Compartilhado por [Nome]"
- **Botões Contextuais**: Baseados na permissão

---

## 📊 Banco de Dados

### **Estrutura de Dados**

#### **Arquivos JSON (Desenvolvimento)**
```
data/
├── config.json           # Configurações da aplicação
├── mindmaps.json         # Mapas mentais dos usuários
├── users.json            # Dados dos usuários cadastrados
└── shared_mindmaps.json  # Compartilhamentos entre usuários
```

#### **Esquemas de Dados**

**Usuário:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "name": "Nome do Usuário",
  "password_hash": "$2b$12$...",
  "created_at": "2025-01-01T00:00:00.000Z",
  "updated_at": "2025-01-01T00:00:00.000Z"
}
```

**Mapa Mental:**
```json
{
  "id": "uuid",
  "name": "Título do Mapa",
  "description": "Descrição opcional",
  "data": {
    "nodes": [...],
    "connections": [...]
  },
  "user_id": "uuid_do_usuario",
  "created_at": "2025-01-01T00:00:00.000Z",
  "updated_at": "2025-01-01T00:00:00.000Z"
}
```

**Compartilhamento:**
```json
{
  "id": "uuid",
  "mindmap_id": "uuid_do_mapa",
  "owner_id": "uuid_do_proprietario",
  "shared_with_id": "uuid_do_destinatario",
  "shared_with_email": "<EMAIL>",
  "permission": "view|edit",
  "created_at": "2025-01-01T00:00:00.000Z"
}
```

### **Backup e Migração**

#### **Backup Local**
```bash
# Backup completo
cp -r ./data ./backup-$(date +%Y%m%d)

# Backup específico
cp ./data/mindmaps.json ./backup-mindmaps.json
```

#### **Migração para PostgreSQL**
1. Configure `DATABASE_URL` no `.env.local`
2. Reinicie a aplicação
3. Dados migrados automaticamente
4. Arquivos JSON mantidos como backup

---

## 🚀 Deploy

### **Vercel (Recomendado)**

1. **Fork o repositório** no GitHub
2. **Conecte no Vercel**:
   ```bash
   npm i -g vercel
   vercel --prod
   ```
3. **Configure variáveis de ambiente**:
   - `JWT_SECRET`: Chave secreta forte
   - `DATABASE_URL`: PostgreSQL (Neon/Supabase)
   - `OPENAI_API_KEY`: Chave da OpenAI

### **Docker**

```dockerfile
# Dockerfile incluído no projeto
docker build -t mindscape .
docker run -p 3000:3000 mindscape
```

### **VPS/Servidor**

```bash
# Clone e configure
git clone https://github.com/leoaalvsufg/mindscape.git
cd mindscape
npm install
npm run build

# Configure variáveis de ambiente
cp .env.example .env.local
# Edite .env.local com suas configurações

# Execute em produção
npm start
```

### **Configurações de Produção**

#### **Variáveis Obrigatórias**
- `JWT_SECRET`: Chave única e forte
- `DATABASE_URL`: PostgreSQL para persistência
- `NODE_ENV=production`

#### **Recomendações**
- **HTTPS**: Obrigatório para cookies seguros
- **Backup**: Automatize backup do banco
- **Monitoramento**: Configure logs e alertas
- **CDN**: Use para arquivos estáticos

---

## 📖 Documentação

### **Documentação Técnica**
- **[Autenticação](./docs/AUTHENTICATION.md)** - Sistema de usuários e segurança
- **[Compartilhamento](./docs/SHARING.md)** - Colaboração entre usuários
- **[APIs](./docs/API.md)** - Referência completa das APIs
- **[Banco de Dados](./docs/DATABASE.md)** - Estrutura e migração

### **Guias de Uso**
- **[Primeiros Passos](./docs/GETTING_STARTED.md)** - Tutorial completo
- **[Configuração Avançada](./docs/ADVANCED_CONFIG.md)** - Configurações detalhadas
- **[Solução de Problemas](./docs/TROUBLESHOOTING.md)** - Problemas comuns

---

## 🧪 Testes

### **Executar Testes**
```bash
# Testes unitários
npm run test

# Testes de integração
npm run test:integration

# Testes E2E
npm run test:e2e

# Coverage
npm run test:coverage
```

### **Cenários de Teste**

#### **Autenticação**
- ✅ Registro de usuário
- ✅ Login e logout
- ✅ Validação de senhas
- ✅ Sessões JWT

#### **Mapas Mentais**
- ✅ Criação e edição
- ✅ Salvamento automático
- ✅ Geração por IA
- ✅ Exportação

#### **Compartilhamento**
- ✅ Compartilhar por email
- ✅ Permissões (ver/editar)
- ✅ Remoção de acesso
- ✅ Validações de segurança

---

## 🔧 Desenvolvimento

### **Scripts Disponíveis**
```bash
# Desenvolvimento
npm run dev          # Servidor de desenvolvimento
npm run build        # Build para produção
npm run start        # Executar produção
npm run lint         # Verificar código
npm run type-check   # Verificar tipos TypeScript
```

### **Estrutura do Projeto**
```
mindscape/
├── app/                    # Next.js App Router
│   ├── api/               # APIs serverless
│   ├── globals.css        # Estilos globais
│   └── page.tsx           # Página principal
├── components/            # Componentes React
│   ├── ui/               # Componentes base (shadcn/ui)
│   ├── auth/             # Componentes de autenticação
│   └── mind-map-editor.tsx
├── lib/                   # Utilitários e serviços
│   ├── database.ts       # Funções do banco de dados
│   ├── auth.ts           # Utilitários de autenticação
│   └── utils.ts          # Funções auxiliares
├── types/                 # Tipos TypeScript
│   └── mindmap.ts        # Interfaces principais
├── data/                  # Banco de dados JSON
│   ├── config.json       # Configurações
│   ├── mindmaps.json     # Mapas mentais
│   ├── users.json        # Usuários
│   └── shared_mindmaps.json
├── docs/                  # Documentação
└── public/               # Arquivos estáticos
```

### **Contribuindo**

1. **Fork o projeto**
2. **Crie uma branch**: `git checkout -b feature/nova-funcionalidade`
3. **Commit suas mudanças**: `git commit -m 'Add nova funcionalidade'`
4. **Push para a branch**: `git push origin feature/nova-funcionalidade`
5. **Abra um Pull Request**

---

## 🐛 Solução de Problemas

### **Problemas Comuns**

#### **Banco de dados não conecta**
```bash
# Verificar permissões
ls -la ./data/

# Recriar banco
rm -rf ./data/
npm run dev  # Recria automaticamente
```

#### **IA não responde**
- ✅ Verifique a chave da API nas configurações
- ✅ Teste a conexão na interface
- ✅ Verifique logs do console (F12)
- ✅ Confirme saldo da API (OpenAI/Anthropic)

#### **Erro de autenticação**
```bash
# Verificar JWT_SECRET
echo $JWT_SECRET

# Limpar cookies
# Abra DevTools → Application → Cookies → Limpar
```

#### **Performance lenta**
- ✅ Limite mapas com muitos nós (>100)
- ✅ Use zoom para focar áreas específicas
- ✅ Feche abas desnecessárias do navegador
- ✅ Verifique recursos do sistema

### **Logs e Debug**

#### **Logs do Servidor**
```bash
# Desenvolvimento
npm run dev  # Logs no terminal

# Produção
pm2 logs mindscape  # Se usando PM2
```

#### **Logs do Cliente**
- Abra **DevTools** (F12)
- Vá para **Console**
- Procure por erros em vermelho

---

## 🤝 Contribuição

### **Como Contribuir**

Contribuições são sempre bem-vindas! Aqui estão algumas formas de ajudar:

- 🐛 **Reportar bugs** através das Issues
- 💡 **Sugerir funcionalidades** novas
- 📖 **Melhorar documentação**
- 🧪 **Escrever testes**
- 🔧 **Corrigir problemas** existentes

### **Diretrizes**

1. **Siga o padrão de código** existente
2. **Escreva testes** para novas funcionalidades
3. **Documente** mudanças significativas
4. **Use commits semânticos**: `feat:`, `fix:`, `docs:`

### **Roadmap**

#### **Próximas Funcionalidades**
- [ ] **Temas personalizáveis** (claro/escuro)
- [ ] **Colaboração em tempo real** (WebSockets)
- [ ] **Versionamento de mapas** (histórico)
- [ ] **Templates predefinidos** de mapas
- [ ] **Integração com Notion/Obsidian**
- [ ] **App mobile** (React Native)

#### **Melhorias Técnicas**
- [ ] **Testes automatizados** completos
- [ ] **CI/CD pipeline** com GitHub Actions
- [ ] **Monitoramento** e analytics
- [ ] **Cache inteligente** para performance
- [ ] **Internacionalização** (i18n)

---

## 📄 Licença

Este projeto está licenciado sob a **MIT License** - veja o arquivo [LICENSE](LICENSE) para detalhes.

### **MIT License**
```
Copyright (c) 2025 Mindscape

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

<div align="center">

**🧠 Desenvolvido com ❤️ para a comunidade**

[![GitHub](https://img.shields.io/badge/GitHub-leoaalvsufg/mindscape-blue?style=for-the-badge&logo=github)](https://github.com/leoaalvsufg/mindscape)
[![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)](LICENSE)

**[⬆️ Voltar ao topo](#-mindscape---mapas-mentais-com-ia)**

</div>

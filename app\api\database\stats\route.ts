import { NextResponse } from "next/server"
import { getDatabaseStats } from "@/lib/database"

export async function GET() {
  try {
    const stats = await getDatabaseStats()
    return NextResponse.json({ success: true, stats })
  } catch (error) {
    console.error("Error getting database stats:", error)
    return NextResponse.json({ success: false, error: "Failed to get database stats" }, { status: 500 })
  }
}

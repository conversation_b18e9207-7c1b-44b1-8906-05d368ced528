#!/usr/bin/env tsx

import { testDatabaseConnection, healthMonitor } from "../lib/neon-connection"
import { migrationManager } from "../lib/migration-manager"
import { checkCriticalEnvVars, logEnvStatus } from "../lib/env"
import { SecurityManager } from "../lib/security"

async function runDeploymentChecks() {
  console.log("🚀 Iniciando verificações de deployment...\n")

  let allChecksPassed = true

  // 1. Verificar variáveis de ambiente
  console.log("1️⃣ Verificando variáveis de ambiente...")
  logEnvStatus()
 
  const envCheck = checkCriticalEnvVars()
  if (!envCheck.valid) {
    console.error("❌ Variáveis de ambiente críticas não configuradas:")
    envCheck.missing.forEach((missing) => console.error(`  - ${missing}`))
    allChecksPassed = false
  } else {
    console.log("✅ Todas as variáveis de ambiente estão configuradas")
  }

  if (envCheck.warnings.length > 0) {
    console.warn("⚠️ Avisos:")
    envCheck.warnings.forEach((warning) => console.warn(`  - ${warning}`))
  }

  console.log()

  // 2. Testar conexão com banco de dados
  console.log("2️⃣ Testando conexão com banco de dados...")
  const dbTest = await testDatabaseConnection()

  if (dbTest.success) {
    console.log("✅ Conexão com banco de dados OK")
    console.log(`  Database: ${dbTest.details?.database}`)
    console.log(`  User: ${dbTest.details?.user}`)
    console.log(`  Tempo de resposta: ${dbTest.metrics?.totalTime}ms`)
  } else {
    console.error("❌ Falha na conexão com banco de dados:")
    console.error(`  ${dbTest.message}`)
    allChecksPassed = false
  }

  console.log()

  // 3. Verificar status das migrações
  console.log("3️⃣ Verificando migrações do banco...")
  try {
    const migrationStatus = await migrationManager.getMigrationStatus()

    if (migrationStatus.pending.length > 0) {
      console.log("🔄 Executando migrações pendentes...")
      await migrationManager.runMigrations()
      console.log("✅ Migrações executadas com sucesso")
    } else {
      console.log("✅ Todas as migrações estão atualizadas")
    }

    console.log(`  Total: ${migrationStatus.total}`)
    console.log(`  Executadas: ${migrationStatus.executed}`)
  } catch (error) {
    console.error("❌ Erro nas migrações:", error)
    allChecksPassed = false
  }

  console.log()

  // 4. Verificar configurações de segurança
  console.log("4️⃣ Verificando configurações de segurança...")
  try {
    // Testar criptografia
    const testData = "test-encryption-data"
    const encrypted = SecurityManager.encrypt(testData)
    const decrypted = SecurityManager.decrypt(encrypted)

    if (decrypted === testData) {
      console.log("✅ Sistema de criptografia funcionando")
    } else {
      console.error("❌ Falha no sistema de criptografia")
      allChecksPassed = false
    }

    // Verificar headers de segurança
    const securityHeaders = SecurityManager.getSecurityHeaders()
    if (Object.keys(securityHeaders).length > 0) {
      console.log("✅ Headers de segurança configurados")
    }
  } catch (error) {
    console.error("❌ Erro nas verificações de segurança:", error)
    allChecksPassed = false
  }

  console.log()

  // 5. Verificar health monitor
  console.log("5️⃣ Verificando sistema de monitoramento...")
  try {
    const healthStatus = healthMonitor.getStatus()

    if (healthStatus.healthy) {
      console.log("✅ Sistema de monitoramento ativo")
      console.log(`  Última verificação: ${healthStatus.lastCheck}`)
      console.log(`  Conexões ativas: ${healthStatus.connectionStats.activeConnections}`)
    } else {
      console.warn("⚠️ Sistema de monitoramento reporta problemas")
      console.warn(`  Falhas consecutivas: ${healthStatus.consecutiveFailures}`)
    }
  } catch (error) {
    console.error("❌ Erro no sistema de monitoramento:", error)
    allChecksPassed = false
  }

  console.log()

  // Resultado final
  if (allChecksPassed) {
    console.log("🎉 Todas as verificações passaram! A aplicação está pronta para deploy.")
    process.exit(0)
  } else {
    console.error("💥 Algumas verificações falharam. Corrija os problemas antes do deploy.")
    process.exit(1)
  }
}

// Executar verificações
runDeploymentChecks().catch((error) => {
  console.error("💥 Erro fatal nas verificações de deployment:", error)
  process.exit(1)
})

import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase } from "@/lib/database"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, url } = body

    const result = await initializeDatabase(type || "neon", url)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Database initialization error:", error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : "Database initialization failed" },
      { status: 500 },
    )
  }
}

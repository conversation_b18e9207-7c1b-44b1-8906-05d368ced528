"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Brain, Users, Save } from "lucide-react"

interface GuestLimitsContextType {
  intelligentUsesLeft: number
  useIntelligentFeature: () => boolean
  resetLimits: () => void
  showSignupModal: () => void
}

const GuestLimitsContext = createContext<GuestLimitsContextType | undefined>(undefined)

interface GuestLimitsProviderProps {
  children: React.ReactNode
  onShowSignupModal: () => void
}

export function GuestLimitsProvider({ children, onShowSignupModal }: GuestLimitsProviderProps) {
  const { user } = useAuth()
  const [intelligentUsesLeft, setIntelligentUsesLeft] = useState(2)
  const [showLimitModal, setShowLimitModal] = useState(false)

  // Reset limits when user logs in
  useEffect(() => {
    if (user) {
      setIntelligentUsesLeft(2) // Reset for logged users (unlimited)
    }
  }, [user])

  const useIntelligentFeature = (): boolean => {
    // Logged users have unlimited access
    if (user) {
      return true
    }

    // Guest users have limited access
    if (intelligentUsesLeft > 0) {
      setIntelligentUsesLeft(prev => prev - 1)
      return true
    }

    // No uses left, show signup modal
    showSignupModal()
    return false
  }

  const resetLimits = () => {
    setIntelligentUsesLeft(2)
  }

  const showSignupModal = () => {
    setShowLimitModal(true)
  }

  const value: GuestLimitsContextType = {
    intelligentUsesLeft,
    useIntelligentFeature,
    resetLimits,
    showSignupModal
  }

  return (
    <GuestLimitsContext.Provider value={value}>
      {children}

      {/* Modal de Limitação de Sugestões Inteligentes */}
      <Dialog open={showLimitModal} onOpenChange={setShowLimitModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-yellow-600" />
              Limite de sugestões atingido
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="text-center">
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">
                  ✨ Você usou suas 2 sugestões inteligentes gratuitas!
                </h3>
                <p className="text-sm text-gray-600">
                  Crie uma conta para ter acesso ilimitado às sugestões de IA
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Sugestões ilimitadas</p>
                  <p className="text-xs text-gray-500">Use IA para conectar ideias sem limites</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Save className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Salvar seus mapas</p>
                  <p className="text-xs text-gray-500">Acesse seus mapas a qualquer momento</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Brain className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Mapas ilimitados</p>
                  <p className="text-xs text-gray-500">Crie quantos mapas quiser, sem limitações</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Compartilhar mapas</p>
                  <p className="text-xs text-gray-500">Colabore com outros usuários</p>
                </div>
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowLimitModal(false)}
                className="flex-1"
              >
                Continuar sem IA
              </Button>
              <Button
                onClick={() => {
                  setShowLimitModal(false)
                  onShowSignupModal()
                }}
                className="flex-1 bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700"
              >
                Criar Conta Grátis
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </GuestLimitsContext.Provider>
  )
}

export function useGuestLimits() {
  const context = useContext(GuestLimitsContext)
  if (context === undefined) {
    throw new Error('useGuestLimits must be used within a GuestLimitsProvider')
  }
  return context
}

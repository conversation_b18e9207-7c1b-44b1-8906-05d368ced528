# 🚀 Deploy do Mindscape - <PERSON><PERSON><PERSON>to

Este guia explica como fazer o deploy da aplicação Mindscape no servidor de produção.

## 📋 Informações do Servidor

- **IP**: **************
- **Usuário**: usuario
- **Senha**: Senh@01020304
- **Porta da Aplicação**: 3030
- **Diretório**: /home/<USER>/mindscape-app

## 🔧 Deploy Automatizado

### Opção 1: Script Automatizado (Recomendado)

```bash
# Dar permissão de execução ao script
chmod +x deploy.sh

# Executar o deploy
./deploy.sh
```

O script automatizado irá:
1. ✅ Criar pacote da aplicação
2. ✅ Conectar ao servidor via SSH
3. ✅ Instalar Node.js 18+ se necessário
4. ✅ Instalar PM2 para gerenciamento de processos
5. ✅ Fazer upload da aplicação
6. ✅ Instalar dependências
7. ✅ Fazer build da aplicação
8. ✅ Configurar PM2
9. ✅ Iniciar aplicação na porta 3030

### Opção 2: Deploy Manual

Se preferir fazer o deploy manualmente:

```bash
# 1. Conectar ao servidor
ssh usuario@**************

# 2. Instalar Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Instalar PM2
sudo npm install -g pm2

# 4. Criar diretório da aplicação
mkdir -p /home/<USER>/mindscape-app
cd /home/<USER>/mindscape-app

# 5. Fazer upload dos arquivos (do seu computador local)
# scp -r . usuario@**************:/home/<USER>/mindscape-app/

# 6. Instalar dependências
npm install --production

# 7. Fazer build
npm run build

# 8. Iniciar com PM2
pm2 start ecosystem.config.js
pm2 save
```

## 🌐 Acesso à Aplicação

Após o deploy bem-sucedido, a aplicação estará disponível em:

**http://**************:3030**

## 📊 Monitoramento e Gerenciamento

### Comandos PM2 Úteis

```bash
# Conectar ao servidor
ssh usuario@**************

# Ver status da aplicação
pm2 status

# Ver logs em tempo real
pm2 logs mindscape

# Ver logs das últimas 100 linhas
pm2 logs mindscape --lines 100

# Reiniciar aplicação
pm2 restart mindscape

# Parar aplicação
pm2 stop mindscape

# Iniciar aplicação
pm2 start mindscape

# Recarregar aplicação (zero downtime)
pm2 reload mindscape

# Ver informações detalhadas
pm2 show mindscape

# Ver uso de CPU e memória
pm2 monit
```

### Logs da Aplicação

Os logs ficam salvos em:
- **Logs de erro**: `/home/<USER>/mindscape-app/logs/err.log`
- **Logs de saída**: `/home/<USER>/mindscape-app/logs/out.log`
- **Logs combinados**: `/home/<USER>/mindscape-app/logs/combined.log`

```bash
# Ver logs de erro
tail -f /home/<USER>/mindscape-app/logs/err.log

# Ver logs de saída
tail -f /home/<USER>/mindscape-app/logs/out.log

# Ver todos os logs
tail -f /home/<USER>/mindscape-app/logs/combined.log
```

## 🔄 Atualizações da Aplicação

Para atualizar a aplicação com novas versões:

```bash
# 1. Execute o script de deploy novamente
./deploy.sh

# OU manualmente:
# 2. Conectar ao servidor
ssh usuario@**************

# 3. Ir para o diretório da aplicação
cd /home/<USER>/mindscape-app

# 4. Fazer backup dos dados (se necessário)
cp -r data data_backup_$(date +%Y%m%d_%H%M%S)

# 5. Fazer upload dos novos arquivos
# 6. Instalar novas dependências
npm install --production

# 7. Fazer novo build
npm run build

# 8. Reiniciar aplicação
pm2 restart mindscape
```

## 🔒 Configurações de Segurança

### Firewall

Certifique-se de que a porta 3030 está aberta:

```bash
# Ubuntu/Debian
sudo ufw allow 3030

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3030/tcp
sudo firewall-cmd --reload
```

### SSL/HTTPS (Opcional)

Para configurar HTTPS, você pode usar nginx como proxy reverso:

```bash
# Instalar nginx
sudo apt-get install nginx

# Configurar proxy reverso
sudo nano /etc/nginx/sites-available/mindscape

# Conteúdo do arquivo:
server {
    listen 80;
    server_name **************;

    location / {
        proxy_pass http://localhost:3030;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Ativar configuração
sudo ln -s /etc/nginx/sites-available/mindscape /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🛠️ Troubleshooting

### Problemas Comuns

1. **Aplicação não inicia**
   ```bash
   pm2 logs mindscape
   # Verificar logs de erro
   ```

2. **Porta já em uso**
   ```bash
   sudo lsof -i :3030
   # Matar processo se necessário
   sudo kill -9 <PID>
   ```

3. **Falta de memória**
   ```bash
   free -h
   # Verificar uso de memória
   pm2 restart mindscape
   ```

4. **Permissões de arquivo**
   ```bash
   sudo chown -R usuario:usuario /home/<USER>/mindscape-app
   chmod -R 755 /home/<USER>/mindscape-app
   ```

### Verificação de Saúde

```bash
# Verificar se a aplicação está respondendo
curl http://localhost:3030

# Verificar status do processo
pm2 status

# Verificar uso de recursos
pm2 monit
```

## 📞 Suporte

Se encontrar problemas durante o deploy:

1. Verifique os logs: `pm2 logs mindscape`
2. Verifique o status: `pm2 status`
3. Reinicie a aplicação: `pm2 restart mindscape`
4. Verifique conectividade: `curl http://localhost:3030`

## 🎉 Deploy Concluído!

Após seguir este guia, sua aplicação Mindscape estará rodando em:

**🌐 http://**************:3030**

Funcionalidades disponíveis:
- ✅ Criação de mapas mentais com IA
- ✅ Sistema de usuários e autenticação
- ✅ Compartilhamento de mapas
- ✅ Proteção por senha
- ✅ Limitações para usuários não logados
- ✅ Sugestões inteligentes
- ✅ Interface responsiva
- ✅ Persistência de dados em JSON

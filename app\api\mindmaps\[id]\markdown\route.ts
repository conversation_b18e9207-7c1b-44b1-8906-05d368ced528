import { NextRequest, NextResponse } from "next/server"
import { getMindMapById } from "@/lib/database"
import { llmService } from "@/lib/llm-service"

export async function GET(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const mindMap = await getMindMapById(params.id)
    if (!mindMap) {
      return NextResponse.json({ error: "Mapa não encontrado" }, { status: 404 })
    }

    const { nodes, connections } = mindMap.data
    if (!nodes?.length) {
      return NextResponse.json({ error: "Mapa vazio" }, { status: 400 })
    }

    // Ordenar nós em DFS começando pelo de nível 0 (ou primeiro)
    const root = nodes.find((n: any) => n.level === 0) ?? nodes[0]
    const ordered: any[] = []
    const visited = new Set<string>()

    const dfs = (id: string) => {
      const n = nodes.find((x: any) => x.id === id)
      if (!n || visited.has(id)) return
      visited.add(id)
      ordered.push(n)
      connections
        .filter((c: any) => c.from === id)
        .forEach((c: any) => dfs(c.to))
    }
    dfs(root.id)

    const prompt = `Escreva um texto em Markdown (600 a 900 palavras) explicando de forma coesa todos os tópicos do mapa mental abaixo, seguindo exatamente a ordem apresentada. Utilize títulos, subtítulos e listas se necessário.\n\n${ordered
      .map((n) => `- ${n.content}`)
      .join("\n")}`

    await llmService.initialize()
    const markdown = await llmService.generateResponse(prompt)

    return NextResponse.json({ success: true, markdown })
  } catch (err) {
    console.error("Erro ao gerar markdown:", err)
    return NextResponse.json({ error: "Falha ao gerar markdown" }, { status: 500 })
  }
} 
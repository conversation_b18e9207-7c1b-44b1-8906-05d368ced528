module.exports = {
  apps: [{
    name: 'mindscape',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/mindscape-app',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: '3030',
      // Database configuration for production
      DATABASE_TYPE: 'json', // Using JSON files for simplicity
      // JWT Secret for production (change this!)
      JWT_SECRET: 'mindscape-production-secret-key-change-this-in-production',
      // OpenRouter API configuration
      OPENROUTER_API_KEY: 'test-key', // Replace with real key if needed
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // Restart policy
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
}

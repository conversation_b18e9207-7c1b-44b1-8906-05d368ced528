import { NextResponse } from "next/server"
import { runMigrations, testConnection } from "@/lib/neon-database"

export async function POST() {
  try {
    console.log("🚀 Iniciando processo de migração...")

    // Testar conexão primeiro
    const connectionTest = await testConnection()
    if (!connectionTest.success) {
      return NextResponse.json(
        {
          success: false,
          message: `Falha na conexão: ${connectionTest.message}`,
          details: connectionTest.details,
        },
        { status: 503 },
      )
    }

    // Executar migrações
    const migrationResult = await runMigrations()

    if (migrationResult.success) {
      console.log("✅ Migrações executadas com sucesso!")

      return NextResponse.json({
        success: true,
        message: "Migrações executadas com sucesso!",
        connection: connectionTest.details,
        timestamp: new Date().toISOString(),
      })
    } else {
      console.error("❌ Falha nas migrações:", migrationResult.message)

      return NextResponse.json(
        {
          success: false,
          message: migrationResult.message,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("❌ Erro crítico nas migrações:", error)

    return NextResponse.json(
      {
        success: false,
        message: `Erro crítico: ${error}`,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}

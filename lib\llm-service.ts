import { getConfig } from "./database"

export interface LLMModel {
  id: string
  name: string
  provider: "openrouter" | "ollama"
  description?: string
  context_length?: number
  pricing?: {
    prompt: number
    completion: number
  }
}

const DEFAULT_OPENROUTER_MODELS: LLMModel[] = [
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "openrouter",
    description: "Most advanced OpenAI model",
    context_length: 128000,
    pricing: { prompt: 0.005, completion: 0.015 },
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "openrouter",
    description: "Faster and more economical GPT-4o",
    context_length: 128000,
    pricing: { prompt: 0.00015, completion: 0.0006 },
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "openrouter",
    description: "Excellent for analysis and reasoning",
    context_length: 200000,
    pricing: { prompt: 0.003, completion: 0.015 },
  },
]

export class LLMService {
  private static instance: LLMService
  private models: LLMModel[] = []
  private config: any = {}

  private constructor() {}

  static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService()
    }
    return LLMService.instance
  }

  async initialize(): Promise<void> {
    try {
      this.config = await getConfig()
      await this.loadModels()
    } catch (error) {
      console.error("Failed to initialize LLM service:", error)
    }
  }

  async loadModels(): Promise<LLMModel[]> {
    this.models = []

    // Always include default OpenRouter models
    this.models.push(...DEFAULT_OPENROUTER_MODELS)

    // Load Ollama models if configured
    if (this.config.llm_provider === "ollama" && this.config.ollama_base_url) {
      try {
        const ollamaModels = await this.fetchOllamaModels()
        this.models.push(...ollamaModels)
      } catch (error) {
        console.error("Failed to load Ollama models:", error)
      }
    }

    return this.models
  }

  private async fetchOllamaModels(): Promise<LLMModel[]> {
    try {
      const baseUrl = this.config.ollama_base_url?.replace(/\/$/, "") || "http://localhost:11434"
      const response = await fetch(`${baseUrl}/api/tags`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`)
      }

      const data = await response.json()
      return (data.models || []).map((model: any) => ({
        id: model.name,
        name: model.name.split(":")[0].toUpperCase(),
        provider: "ollama" as const,
        description: `Local Ollama model: ${model.name}`,
      }))
    } catch (error) {
      console.error("Error fetching Ollama models:", error)
      return []
    }
  }

  async testConnection(provider: "openrouter" | "ollama", config: any): Promise<{ success: boolean; message: string }> {
    try {
      if (provider === "openrouter") {
        if (!config.apiKey) {
          return { success: false, message: "API key is required" }
        }

        const response = await fetch("https://openrouter.ai/api/v1/models", {
          headers: {
            Authorization: `Bearer ${config.apiKey}`,
            "Content-Type": "application/json",
          },
        })

        if (response.ok) {
          return { success: true, message: "OpenRouter connection successful" }
        } else {
          const errorText = await response.text()
          return { success: false, message: `OpenRouter error: ${response.status} - ${errorText}` }
        }
      } else if (provider === "ollama") {
        const baseUrl = config.baseUrl?.replace(/\/$/, "") || "http://localhost:11434"
        const response = await fetch(`${baseUrl}/api/tags`)

        if (response.ok) {
          return { success: true, message: "Ollama connection successful" }
        } else {
          return { success: false, message: `Ollama error: ${response.status}` }
        }
      }

      return { success: false, message: "Unknown provider" }
    } catch (error) {
      return { success: false, message: `Connection failed: ${error}` }
    }
  }

  async generateResponse(prompt: string, modelId?: string): Promise<string> {
    const model = modelId || this.config.default_model || "openai/gpt-4o-mini"

    if (this.config.llm_provider === "openrouter") {
      return this.generateOpenRouterResponse(prompt, model)
    } else if (this.config.llm_provider === "ollama") {
      return this.generateOllamaResponse(prompt, model)
    }

    throw new Error("No LLM provider configured")
  }

  private async generateOpenRouterResponse(prompt: string, model: string): Promise<string> {
    if (!this.config.openrouter_api_key) {
      throw new Error("OpenRouter API key not configured")
    }

    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.config.openrouter_api_key}`,
        "Content-Type": "application/json",
        "HTTP-Referer": process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000",
        "X-Title": "Mindscape",
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant that creates mind maps. Always respond in valid JSON format with the structure requested.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || "No response generated"
  }

  private async generateOllamaResponse(prompt: string, model: string): Promise<string> {
    const baseUrl = this.config.ollama_base_url?.replace(/\/$/, "") || "http://localhost:11434"

    const response = await fetch(`${baseUrl}/api/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model,
        prompt,
        stream: false,
        options: {
          temperature: 0.7,
        },
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Ollama API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    return data.response || "No response generated"
  }

  getModels(): LLMModel[] {
    return this.models
  }

  getConfig(): any {
    return this.config
  }

  async updateConfig(newConfig: any): Promise<void> {
    this.config = { ...this.config, ...newConfig }
    await this.loadModels()
  }
}

export const llmService = LLMService.getInstance()

# Design Proposal: Integrated Mind Map UI Architecture

This document outlines the proposed UI architecture for integrating a manual, interactive mind map canvas with a contextual AI chat panel.

## 1. Analysis of Existing Architecture & Core Problem

### 1.1. Existing Component Structure

A review of the current codebase reveals an application structured primarily for generating and viewing mind maps, but not for direct, real-time interaction as required by the new specification.

*   `components/main-app.tsx`: Acts as the main entry point, displaying a library of existing mind maps. It includes functionality to create a new mind map via a text prompt, which triggers a call to the `/api/chat` endpoint. This component is essentially a "gallery" and does not contain the interactive canvas itself.
*   `components/mind-map-viewer.tsx`: This component is responsible for rendering a selected mind map in a read-only fashion. It has its own state for zoom and pan and includes controls for exporting the map. It is not designed for node manipulation or integration with a live chat panel.
*   `components/mind-map-canvas.tsx`: This is a more generic and reusable component that handles the low-level rendering of nodes and connections via SVG. It includes basic logic for panning and single-node dragging, but its state is meant to be managed by a parent component. It currently lacks the more advanced interaction features required, such as context menus, inline editing, or keyboard shortcuts.
*   `components/chat-interface.tsx`: This component provides the UI for the chat functionality. It is designed to work in two modes: "complete map generation" and "question-answering." It is not currently integrated with a live canvas and does not have a mechanism to know which node is "selected" on a canvas.
*   `components/ui/resizable.tsx`: This is a UI primitive from the `shadcn/ui` library that provides the necessary building blocks for creating resizable panels. It is a perfect fit for the required 75/25 layout.

### 1.2. The Core Architectural Challenge

The fundamental challenge is to shift the application's paradigm from a two-step "generate-then-view" model to a single, unified "interactive editor" model. The current architecture separates the creation logic (`main-app.tsx`) and viewing logic (`mind-map-viewer.tsx`) into distinct, disconnected experiences.

The new design requires a persistent, integrated view where the `mind-map-canvas` and `chat-interface` are not only displayed simultaneously but are also deeply interconnected through a shared state. The state of the canvas (e.g., the selected node) must be immediately available to the chat panel, and actions triggered from the chat panel must directly manipulate the state of the canvas. This requires a new top-level component to manage this shared state and a refactoring of existing components to fit into this new, interactive paradigm.
## 2. Proposed Component Architecture

To address this, we will introduce a new primary component, `components/mind-map-editor.tsx`, which will replace the current `mind-map-viewer.tsx` when a user decides to edit or create a mind map. This new component will serve as the container for the entire interactive experience.

### 2.1. New Component Hierarchy

The new component structure will be organized as follows:

```
- components/
  - mind-map-editor.tsx (New)
    - ResizablePanelGroup (from ui/resizable)
      - ResizablePanel (Canvas Area - 75%)
        - mind-map-canvas.tsx (Refactored)
          - mind-map-node.tsx (Refactored)
          - node-context-menu.tsx (New)
      - ResizableHandle (from ui/resizable)
      - ResizablePanel (Chat Area - 25%)
        - chat-interface.tsx (Refactored)
  - main-app.tsx (Refactored)
```

### 2.2. Component Breakdown

*   **`mind-map-editor.tsx` (New):**
    *   **Responsibility:** This will be the main stateful component, managing the entire state of a single mind map session, including all nodes, connections, the `selectedNodeId`, zoom/pan state, and the chat history.
    *   It will implement the 75/25 resizable layout using `ResizablePanelGroup`.
    *   It will fetch the initial mind map data if editing an existing map or initialize a blank state for a new one.
    *   It will house all the core logic for adding, updating, and deleting nodes, whether the action originates from the canvas or the chat.

*   **`mind-map-canvas.tsx` (Refactored):**
    *   **Responsibility:** This component will be refactored to focus solely on rendering the visual representation of the mind map and capturing user input.
    *   It will become a "controlled" component, receiving the list of nodes, the selected node, and event handlers (e.g., `onNodeClick`, `onNodeDoubleClick`, `onCanvasPan`) as props from `mind-map-editor.tsx`.
    *   It will implement the direct manipulation features: drag-and-drop for nodes, click-and-drag for panning, and mouse scroll for zooming.
    *   It will be responsible for triggering the display of the new `node-context-menu.tsx`.

*   **`node-context-menu.tsx` (New):**
    *   **Responsibility:** A simple component that renders the right-click context menu for a node.
    *   It will receive props like `onAddChild`, `onAddSibling`, and `onDelete` to execute the corresponding actions in the parent `mind-map-editor.tsx`.

*   **`mind-map-node.tsx` (Refactored):**
    *   **Responsibility:** To handle the rendering and inline editing of a single node.
    *   The double-click functionality will be added here to switch between a view mode (`<p>` or `<div>`) and an edit mode (`<input>` or `<textarea>`). On blur or `Enter`, it will call the `onNodeUpdate` prop.

*   **`chat-interface.tsx` (Refactored):**
    *   **Responsibility:** To manage the user-facing chat, sending prompts to the AI, and displaying the conversation.
    *   It will be simplified to no longer manage its own "modes" (`complete` vs. `question`). Instead, it will receive the `selectedNodeId` as a prop.
    *   When the user submits a prompt, it will pass both the prompt text and the `selectedNodeId` to the `mind-map-editor.tsx`, which will then handle the API call.

*   **`main-app.tsx` (Refactored):**
    *   **Responsibility:** The initial view of the app, listing saved mind maps.
    *   Instead of opening the `MindMapViewer`, a click on a mind map will now launch the `MindMapEditor`. A "Create New" button will also launch the editor with a blank state.
## 3. State Management Strategy

A robust and efficient state management strategy is crucial for ensuring seamless interaction between the canvas and the chat panel. Given the need to share state across sibling components and to avoid performance bottlenecks from frequent updates (e.g., during node dragging), a dedicated state management library is preferable to relying solely on React's built-in `useState` and prop drilling.

### 3.1. Recommended Library: Zustand

We recommend using **Zustand**, a lightweight, fast, and unopinionated state management solution for React.

*   **Why Zustand?**
    *   **Minimal Boilerplate:** It offers a simple, hook-based API that is easy to learn and integrate.
    *   **Performance:** Zustand avoids the performance issues of a single, large React Context by allowing components to subscribe only to the state slices they need, preventing unnecessary re-renders.
    *   **Developer Experience:** Its simplicity aligns well with the existing hook-based patterns in the codebase (e.g., `ai/react`, `react-hook-form`).

### 3.2. State Store Structure

We will create a single Zustand store within the `components/mind-map-editor.tsx` component. This store will encapsulate all the data and actions related to the current mind map session.

A simplified representation of the store's structure would be:

```typescript
import create from 'zustand';

interface MindMapState {
  nodes: Node[];
  connections: Connection[];
  selectedNodeId: string | null;
  
  // Actions
  selectNode: (nodeId: string | null) => void;
  addNode: (nodeData: Partial<Node>) => void;
  updateNode: (nodeId: string, updates: Partial<Node>) => void;
  deleteNode: (nodeId: string) => void;
  // ... other actions like addConnection, etc.
}

const useMindMapStore = create<MindMapState>((set) => ({
  nodes: [],
  connections: [],
  selectedNodeId: null,
  
  selectNode: (nodeId) => set({ selectedNodeId: nodeId }),
  
  addNode: (nodeData) => set((state) => ({
    nodes: [...state.nodes, { id: generateNewId(), ...nodeData }]
  })),
  
  updateNode: (nodeId, updates) => set((state) => ({
    nodes: state.nodes.map(n => n.id === nodeId ? { ...n, ...updates } : n)
  })),

  deleteNode: (nodeId) => set((state) => {
    // Also remove connections to this node
    const newNodes = state.nodes.filter(n => n.id !== nodeId);
    const newConnections = state.connections.filter(c => c.fromId !== nodeId && c.toId !== nodeId);
    return { nodes: newNodes, connections: newConnections, selectedNodeId: null };
  })
}));

```

### 3.3. State Consumption

*   The **`mind-map-canvas.tsx`** component will use the store to get the `nodes`, `connections`, and `selectedNodeId` for rendering. It will call action functions like `selectNode` or `updateNode` in response to user interactions.
*   The **`chat-interface.tsx`** component will subscribe to the `selectedNodeId` to provide context for the AI. When the AI generates new nodes, the `chat-interface` will call the `addNode` and `addConnection` actions from the store.
## 4. Data Flow for AI Generation

The following sequence outlines the end-to-end data flow for the contextual AI node generation feature. This flow ensures that the chat interaction is directly tied to the canvas state.

### 4.1. Step-by-Step Data Flow

1.  **User Selects Node:** The user clicks on a node in the `mind-map-canvas.tsx` component.
2.  **State Update:** The `onNodeClick` event handler in the canvas calls the `selectNode(nodeId)` action from our Zustand store. The `selectedNodeId` in the store is now updated.
3.  **Chat Interface Reacts:** The `chat-interface.tsx` component, which is subscribed to the `selectedNodeId` from the store, now has the context of the selected node.
4.  **User Sends Prompt:** The user types a prompt (e.g., "Expand on this") into the chat input and submits the form.
5.  **API Request:** The `chat-interface.tsx`'s `handleSubmit` function triggers a call to a function passed from `mind-map-editor.tsx`. This function retrieves the prompt text, the `selectedNodeId`, and the content of the selected node from the store. It then makes a `fetch` request to the backend API (`/api/chat`). The request body will include the prompt and the node's content as context.
6.  **Backend Processing:** The backend API route receives the request, forwards the prompt and context to the LLM, and awaits the response. The API should be designed to return a structured JSON object containing the new nodes to be added (e.g., `{ newNodes: [{ content: "Sub-idea 1" }, { content: "Sub-idea 2" }] }`).
7.  **API Response:** The frontend receives the JSON response from the API.
8.  **State Update (Adding Nodes):** The `mind-map-editor.tsx` component's `fetch` handler parses the response. For each new node in the `newNodes` array, it calls the `addNode()` and `addConnection()` actions from the Zustand store, creating the new nodes and linking them to the originally selected parent node.
9.  **Canvas Re-renders:** The `mind-map-canvas.tsx` component, being subscribed to the `nodes` and `connections` arrays in the store, automatically re-renders to display the newly added nodes and their connections in real-time.

### 4.2. Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Canvas as mind-map-canvas.tsx
    participant Store as Zustand Store
    participant Chat as chat-interface.tsx
    participant Editor as mind-map-editor.tsx
    participant API as /api/chat

    User->>Canvas: 1. Clicks on a node
    Canvas->>Store: 2. selectNode(nodeId)
    Store-->>Chat: 3. Reacts to selectedNodeId change

    User->>Chat: 4. Enters prompt and submits
    Chat->>Editor: 5. Calls handleAiPrompt(prompt, selectedNodeId)
    Editor->>Store: Gets selected node content
    Editor->>API: 6. fetch with prompt and node context
    API->>API: Processes with LLM
    API-->>Editor: 7. Returns JSON with new nodes

    Editor->>Store: 8. addNode() for each new node
    Editor->>Store: 8. addConnection() for each new node
    Store-->>Canvas: 9. Re-renders with new nodes/connections
```
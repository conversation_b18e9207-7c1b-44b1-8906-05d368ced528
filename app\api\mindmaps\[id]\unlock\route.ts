import { NextRequest, NextResponse } from 'next/server'
import { verifyMindMapPassword, getMindMapById } from '@/lib/database'
import { z } from 'zod'

const unlockSchema = z.object({
  password: z.string().min(1, 'Senha é obrigatória')
})

// Verify password and unlock mind map
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params

    const body = await request.json()
    const validation = unlockSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Dados inválidos',
          details: validation.error.errors 
        },
        { status: 400 }
      )
    }

    const { password } = validation.data

    // Verify the mind map exists
    const mindMap = await getMindMapById(mindmapId)
    if (!mindMap) {
      return NextResponse.json(
        { success: false, error: 'Mapa mental não encontrado' },
        { status: 404 }
      )
    }

    // Check if the mind map is protected
    if (!mindMap.is_protected) {
      return NextResponse.json(
        { success: false, error: 'Este mapa mental não está protegido por senha' },
        { status: 400 }
      )
    }

    // Verify the password
    const isValidPassword = await verifyMindMapPassword(mindmapId, password)
    
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Senha incorreta' },
        { status: 401 }
      )
    }

    // Return the unlocked mind map data
    return NextResponse.json({
      success: true,
      message: 'Mapa mental desbloqueado com sucesso',
      mindMap: {
        id: mindMap.id,
        titulo: mindMap.name,
        dados: typeof mindMap.data === 'string' ? JSON.parse(mindMap.data) : mindMap.data,
        criado_em: mindMap.created_at,
        atualizado_em: mindMap.updated_at,
        descricao: mindMap.description,
        user_id: mindMap.user_id,
        is_protected: mindMap.is_protected
      }
    })

  } catch (error: any) {
    console.error('Unlock mind map error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

import { type NextRequest, NextResponse } from "next/server"

interface OpenRouterModel {
  id: string
  name: string
  description?: string
  context_length?: number
  pricing?: {
    prompt?: string
    completion?: string
  }
}

interface OllamaModel {
  name: string
  model: string
  size?: number
  digest?: string
  details?: {
    format?: string
    family?: string
    families?: string[]
    parameter_size?: string
    quantization_level?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const { provider, apiKey, baseUrl } = await request.json()

    if (provider === "openrouter") {
      if (!apiKey) {
        return NextResponse.json({ success: false, error: "API key is required for OpenRouter" }, { status: 400 })
      }

      const response = await fetch("https://openrouter.ai/api/v1/models", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`)
      }

      const data = await response.json()
      const models =
        data.data?.map((model: OpenRouterModel) => ({
          id: model.id,
          name: model.name || model.id,
          description: model.description,
          context_length: model.context_length,
          pricing: model.pricing
            ? {
                prompt: Number.parseFloat(model.pricing.prompt || "0"),
                completion: Number.parseFloat(model.pricing.completion || "0"),
              }
            : undefined,
          provider: "openrouter",
        })) || []

      return NextResponse.json({ success: true, models })
    }

    if (provider === "ollama") {
      const ollamaUrl = baseUrl || "http://localhost:11434"

      const response = await fetch(`${ollamaUrl}/api/tags`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`)
      }

      const data = await response.json()
      const models =
        data.models?.map((model: OllamaModel) => ({
          id: model.name,
          name: model.name,
          description: `Local model - ${model.details?.family || "Unknown family"}`,
          context_length: undefined,
          pricing: undefined,
          provider: "ollama",
        })) || []

      return NextResponse.json({ success: true, models })
    }

    return NextResponse.json({ success: false, error: "Invalid provider" }, { status: 400 })
  } catch (error) {
    console.error("Error fetching models:", error)
    return NextResponse.json({ success: false, error: `Failed to fetch models: ${error}` }, { status: 500 })
  }
}

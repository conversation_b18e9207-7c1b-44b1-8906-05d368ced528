# 🔧 Configuração de Variáveis de Ambiente no Vercel

Este guia detalha como configurar todas as variáveis de ambiente necessárias para o funcionamento da aplicação Mind Map no Vercel.

## 📋 Variáveis Obrigatórias

### 1. NEON_DATABASE_URL
**Descrição**: URL de conexão com o banco de dados Neon
**Formato**: `postgresql://user:<EMAIL>/database?sslmode=require`
**Como obter**:
1. Acesse [neon.tech](https://neon.tech)
2. Crie uma conta e um novo projeto
3. Copie a connection string da dashboard

### 2. SESSION_SECRET
**Descrição**: Chave secreta para sessões (32+ caracteres)
**Formato**: String aleatória de 32+ caracteres
**Como gerar**:
\`\`\`bash
openssl rand -base64 32
\`\`\`

### 3. ENCRYPTION_KEY
**Descrição**: Chave para criptografia de dados (32+ caracteres)
**Formato**: String aleatória de 32+ caracteres
**Como gerar**:
\`\`\`bash
openssl rand -base64 32
\`\`\`

## 🔧 Variáveis Opcionais

| Variável | Valor Padrão | Descrição |
|----------|--------------|-----------|
| VERCEL_REGION | us-east-1 | Região do Vercel |
| MAX_CONNECTIONS | 10 | Máximo de conexões simultâneas |
| CONNECTION_TIMEOUT | 10000 | Timeout de conexão (ms) |
| QUERY_TIMEOUT | 30000 | Timeout de query (ms) |

## 🚀 Métodos de Configuração

### Método 1: Vercel CLI (Recomendado)
\`\`\`bash
# Instalar CLI
npm install -g vercel

# Login
vercel login

# Configurar variáveis
vercel env add NEON_DATABASE_URL
vercel env add SESSION_SECRET
vercel env add ENCRYPTION_KEY
\`\`\`

### Método 2: Dashboard Web
1. Acesse [vercel.com/dashboard](https://vercel.com/dashboard)
2. Selecione seu projeto
3. Vá para **Settings** → **Environment Variables**
4. Adicione cada variável

### Método 3: Script Automatizado
\`\`\`bash
chmod +x scripts/setup-vercel-env.sh
./scripts/setup-vercel-env.sh
\`\`\`

## ✅ Verificação

Após configurar, verifique se tudo está funcionando:

\`\`\`bash
# Health check geral
curl https://your-app.vercel.app/api/health

# Health check do banco
curl https://your-app.vercel.app/api/database/health
\`\`\`

## 🔒 Segurança

- ✅ Nunca commite secrets no código
- ✅ Use valores diferentes para produção e desenvolvimento
- ✅ Rotacione as chaves periodicamente
- ✅ Use HTTPS sempre

## 🐛 Troubleshooting

### Erro: "NEON_DATABASE_URL não configurado"
- Verifique se a variável está definida no Vercel
- Confirme que a URL está no formato correto
- Teste a conexão localmente

### Erro: "SESSION_SECRET muito curto"
- Use pelo menos 32 caracteres
- Gere uma nova chave com `openssl rand -base64 32`

### Erro: "Falha na conexão com o banco"
- Verifique se o banco Neon está ativo
- Confirme as credenciais na URL
- Teste a conectividade de rede

## 📞 Suporte

Se precisar de ajuda:
1. Verifique os logs no Vercel Dashboard
2. Execute o health check: `/api/health`
3. Consulte a documentação do Neon

"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Lock, Unlock, Eye, EyeOff, Loader2, Shield, ShieldOff } from "lucide-react"

interface PasswordProtectionModalProps {
  isOpen: boolean
  onClose: () => void
  mindMapId: string
  mindMapTitle: string
  mode: 'set' | 'unlock' | 'remove'
  onSuccess?: (data?: any) => void
}

export function PasswordProtectionModal({ 
  isOpen, 
  onClose, 
  mindMapId, 
  mindMapTitle, 
  mode,
  onSuccess 
}: PasswordProtectionModalProps) {
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (mode !== 'remove' && !password.trim()) {
      toast({
        title: "Erro",
        description: "Senha é obrigatória",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      let response
      let endpoint
      let method = 'POST'

      switch (mode) {
        case 'set':
          endpoint = `/api/mindmaps/${mindMapId}/password`
          break
        case 'unlock':
          endpoint = `/api/mindmaps/${mindMapId}/unlock`
          break
        case 'remove':
          endpoint = `/api/mindmaps/${mindMapId}/password`
          method = 'DELETE'
          break
      }

      response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: mode !== 'remove' ? JSON.stringify({ password: password.trim() }) : undefined
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Sucesso!",
          description: data.message,
        })
        
        if (onSuccess) {
          onSuccess(data)
        }
        
        setPassword("")
        onClose()
      } else {
        toast({
          title: "Erro",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Password operation error:', error)
      toast({
        title: "Erro",
        description: "Erro interno do servidor",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getModalConfig = () => {
    switch (mode) {
      case 'set':
        return {
          title: 'Proteger com Senha',
          description: 'Defina uma senha para proteger este mapa mental',
          icon: <Lock className="h-5 w-5" />,
          buttonText: 'Proteger Mapa',
          buttonIcon: <Shield className="h-4 w-4 mr-2" />,
          placeholder: 'Digite uma senha forte'
        }
      case 'unlock':
        return {
          title: 'Mapa Protegido',
          description: 'Este mapa mental está protegido por senha',
          icon: <Unlock className="h-5 w-5" />,
          buttonText: 'Desbloquear',
          buttonIcon: <Unlock className="h-4 w-4 mr-2" />,
          placeholder: 'Digite a senha'
        }
      case 'remove':
        return {
          title: 'Remover Proteção',
          description: 'Tem certeza que deseja remover a proteção por senha?',
          icon: <ShieldOff className="h-5 w-5" />,
          buttonText: 'Remover Proteção',
          buttonIcon: <ShieldOff className="h-4 w-4 mr-2" />,
          placeholder: ''
        }
    }
  }

  const config = getModalConfig()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {config.icon}
            {config.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Mind Map Info */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="font-medium text-sm text-gray-900">{mindMapTitle}</p>
            <p className="text-xs text-gray-500">{config.description}</p>
          </div>

          {/* Password Form */}
          {mode !== 'remove' ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">
                  {mode === 'set' ? 'Nova Senha' : 'Senha'}
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={config.placeholder}
                    required
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {mode === 'set' && (
                  <p className="text-xs text-gray-500">
                    Use uma senha forte para proteger seu mapa mental
                  </p>
                )}
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  <>
                    {config.buttonIcon}
                    {config.buttonText}
                  </>
                )}
              </Button>
            </form>
          ) : (
            /* Remove Protection Confirmation */
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <ShieldOff className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800">
                      Atenção
                    </h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Ao remover a proteção, qualquer pessoa com acesso ao mapa poderá visualizá-lo sem senha.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleSubmit}
                  variant="destructive"
                  className="flex-1"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Removendo...
                    </>
                  ) : (
                    <>
                      {config.buttonIcon}
                      {config.buttonText}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

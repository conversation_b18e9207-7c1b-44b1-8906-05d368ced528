"use client"

import { <PERSON>, <PERSON>rk<PERSON> } from "lucide-react"
import { useAuth } from "@/components/auth/auth-provider"
import { useGuestLimits } from "@/components/guest-limits-provider"

export function GuestBanner() {
  const { user } = useAuth()
  const { intelligentUsesLeft } = useGuestLimits()

  if (user) return null

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
      <div className="flex items-center justify-center gap-2 text-blue-800">
        <Brain className="h-5 w-5" />
        <span className="font-medium">Modo Demonstração</span>
      </div>
      <p className="text-sm text-blue-700 mt-2 text-center">
        Experimente gratuitamente! Gere um mapa mental com até 8 nós. 
        <br />
        <strong>Faça login ou cadastre-se</strong> para criar mapas ilimitados e salvá-los.
      </p>
      <div className="flex items-center justify-center gap-2 mt-3 text-blue-700">
        <Sparkles className="h-4 w-4" />
        <span className="text-xs">
          Sugestões inteligentes restantes: <strong>{intelligentUsesLeft}</strong>
        </span>
      </div>
    </div>
  )
}

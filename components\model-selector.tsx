"use client"

import * as React from "react"
import { Check, ChevronsUpDown, RefreshCw, Cloud, Monitor } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useTranslation } from "@/lib/i18n"

interface Model {
  id: string
  name: string
  description?: string
  context_length?: number
  pricing?: {
    prompt?: number
    completion?: number
  }
  provider: "openrouter" | "ollama"
}

interface ModelSelectorProps {
  value?: string
  onValueChange: (value: string) => void
  provider: "openrouter" | "ollama"
  apiKey?: string
  baseUrl?: string
  language: string
}

export function ModelSelector({ value, onValueChange, provider, apiKey, baseUrl, language }: ModelSelectorProps) {
  const { t } = useTranslation(language)
  const [open, setOpen] = React.useState(false)
  const [models, setModels] = React.useState<Model[]>([])
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const loadModels = React.useCallback(async () => {
    if (!apiKey && provider === "openrouter") return
    if (!baseUrl && provider === "ollama") return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/models", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider,
          apiKey,
          baseUrl,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setModels(data.models || [])
      } else {
        setError(data.error || "Failed to load models")
      }
    } catch (err) {
      console.error("Error loading models:", err)
      setError(err instanceof Error ? err.message : "Failed to load models")
    } finally {
      setLoading(false)
    }
  }, [provider, apiKey, baseUrl])

  React.useEffect(() => {
    loadModels()
  }, [loadModels])

  const selectedModel = models.find((model) => model.id === value)

  const formatPrice = (price?: number) => {
    if (!price) return "Free"
    if (price < 0.001) return `$${(price * 1000000).toFixed(2)}/1M tokens`
    if (price < 1) return `$${(price * 1000).toFixed(2)}/1K tokens`
    return `$${price.toFixed(2)}/1K tokens`
  }

  const formatContextLength = (length?: number) => {
    if (!length) return "Unknown"
    if (length >= 1000000) return `${(length / 1000000).toFixed(1)}M`
    if (length >= 1000) return `${(length / 1000).toFixed(0)}K`
    return length.toString()
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">{t.selectModel}</label>
        <Button variant="ghost" size="sm" onClick={loadModels} disabled={loading} className="h-8 px-2">
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
        </Button>
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-transparent"
            disabled={loading || models.length === 0}
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : selectedModel ? (
              <div className="flex items-center space-x-2 truncate">
                {provider === "ollama" ? (
                  <Monitor className="h-4 w-4 text-blue-500" />
                ) : (
                  <Cloud className="h-4 w-4 text-green-500" />
                )}
                <span className="truncate">{selectedModel.name}</span>
              </div>
            ) : (
              <span className="text-muted-foreground">{t.selectModel}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder={t.searchModels} />
            <CommandList>
              <CommandEmpty>
                {error ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-destructive">{error}</p>
                    <Button variant="ghost" size="sm" onClick={loadModels} className="mt-2">
                      {t.tryAgain}
                    </Button>
                  </div>
                ) : (
                  t.noModelsFound
                )}
              </CommandEmpty>
              {models.length > 0 && (
                <CommandGroup heading={provider === "ollama" ? t.localModels : t.cloudModels}>
                  {models.map((model) => (
                    <CommandItem
                      key={model.id}
                      value={model.id}
                      onSelect={(currentValue) => {
                        onValueChange(currentValue === value ? "" : currentValue)
                        setOpen(false)
                      }}
                      className="flex flex-col items-start space-y-1 py-3"
                    >
                      <div className="flex items-center space-x-2 w-full">
                        <Check className={cn("h-4 w-4", value === model.id ? "opacity-100" : "opacity-0")} />
                        {provider === "ollama" ? (
                          <Monitor className="h-4 w-4 text-blue-500" />
                        ) : (
                          <Cloud className="h-4 w-4 text-green-500" />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{model.name}</div>
                          <div className="text-xs text-muted-foreground truncate">{model.id}</div>
                        </div>
                      </div>

                      {model.description && (
                        <div className="text-xs text-muted-foreground ml-10 line-clamp-2">{model.description}</div>
                      )}

                      <div className="flex items-center space-x-2 ml-10">
                        {model.context_length && (
                          <Badge variant="secondary" className="text-xs">
                            {formatContextLength(model.context_length)} ctx
                          </Badge>
                        )}
                        {model.pricing && (
                          <Badge variant="outline" className="text-xs">
                            {formatPrice(model.pricing.prompt)}
                          </Badge>
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {error && <p className="text-xs text-destructive">{error}</p>}
    </div>
  )
}

-- <PERSON><PERSON>r tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS configuracoes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chave TEXT UNIQUE NOT NULL,
    valor TEXT NOT NULL,
    descricao TEXT,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON>r tabel<PERSON> de mapas mentais
CREATE TABLE IF NOT EXISTS mapas_mentais (
    id TEXT PRIMARY KEY,
    nome TEXT NOT NULL,
    descricao TEXT,
    nos TEXT NOT NULL, -- JSON dos nós
    conexoes TEXT NOT NULL, -- JSON das conexões
    protegido_senha BOOLEAN DEFAULT FALSE,
    hash_senha TEXT, -- Hash da senha se protegido
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Inserir configurações padrão atualizadas
INSERT OR IGNORE INTO configuracoes (chave, valor, descricao) VALUES 
('llm_provider', 'openrouter', 'Provedor de IA (openrouter/ollama)'),
('openrouter_api_key', '', 'Chave da API do OpenRouter'),
('ollama_base_url', 'http://localhost:11434', 'URL base do servidor Ollama'),
('modelo_padrao', 'openai/gpt-4o', 'Modelo de IA padrão'),
('tema', 'claro', 'Tema da interface (claro/escuro)'),
('auto_salvar', 'true', 'Salvar automaticamente a cada 5 minutos'),
('zoom_padrao', '1', 'Nível de zoom padrão'),
('grade_visivel', 'true', 'Mostrar grade no canvas');

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_mapas_criado_em ON mapas_mentais(criado_em);
CREATE INDEX IF NOT EXISTS idx_mapas_protegido ON mapas_mentais(protegido_senha);
CREATE INDEX IF NOT EXISTS idx_configuracoes_chave ON configuracoes(chave);

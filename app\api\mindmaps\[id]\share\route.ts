import { NextRequest, NextResponse } from 'next/server'
import { shareMindMap, getMindMapShares, removeShare, getMindMapById } from '@/lib/database'
import { getUserFromRequest } from '@/lib/middleware'
import { z } from 'zod'

const shareSchema = z.object({
  email: z.string().email('Email inválido'),
  permission: z.enum(['view', 'edit']).default('view')
})

// Share a mind map with another user
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    // Verify the user owns this mind map
    const mindMap = await getMindMapById(mindmapId)
    if (!mindMap || mindMap.user_id !== user.userId) {
      return NextResponse.json(
        { success: false, error: 'Mapa mental não encontrado ou sem permissão' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const validation = shareSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Dados inválidos',
          details: validation.error.errors 
        },
        { status: 400 }
      )
    }

    const { email, permission } = validation.data

    // Don't allow sharing with yourself
    if (email === user.email) {
      return NextResponse.json(
        { success: false, error: 'Você não pode compartilhar com você mesmo' },
        { status: 400 }
      )
    }

    const shareId = await shareMindMap(mindmapId, user.userId, email, permission)

    return NextResponse.json({
      success: true,
      shareId,
      message: `Mapa mental compartilhado com ${email}`
    })

  } catch (error: any) {
    console.error('Share mind map error:', error)
    
    if (error.message === 'User not found') {
      return NextResponse.json(
        { success: false, error: 'Usuário não encontrado com este email' },
        { status: 404 }
      )
    }
    
    if (error.message === 'Mind map already shared with this user') {
      return NextResponse.json(
        { success: false, error: 'Mapa mental já compartilhado com este usuário' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Get all shares for a mind map
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    // Verify the user owns this mind map
    const mindMap = await getMindMapById(mindmapId)
    if (!mindMap || mindMap.user_id !== user.userId) {
      return NextResponse.json(
        { success: false, error: 'Mapa mental não encontrado ou sem permissão' },
        { status: 404 }
      )
    }

    const shares = await getMindMapShares(mindmapId, user.userId)

    return NextResponse.json({
      success: true,
      shares
    })

  } catch (error) {
    console.error('Get mind map shares error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Remove a share
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: mindmapId } = params
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const shareId = searchParams.get('shareId')

    if (!shareId) {
      return NextResponse.json(
        { success: false, error: 'ID do compartilhamento é obrigatório' },
        { status: 400 }
      )
    }

    await removeShare(shareId, user.userId)

    return NextResponse.json({
      success: true,
      message: 'Compartilhamento removido com sucesso'
    })

  } catch (error) {
    console.error('Remove share error:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

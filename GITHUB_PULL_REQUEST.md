# 🚀 Guia para Pull Request no GitHub

## 📋 Pré-requisitos

Antes de enviar o pull request, certifique-se de que:

1. ✅ Todos os arquivos estão commitados
2. ✅ As variáveis de ambiente estão configuradas
3. ✅ A aplicação está funcionando localmente
4. ✅ Os testes passaram

## 🔧 Passos para Pull Request

### 1. **Verificar Status do Git**

\`\`\`bash
# Verificar status atual
git status

# Verificar branch atual
git branch
\`\`\`

### 2. **Adicionar e Commitar Mudanças**

\`\`\`bash
# Adicionar todos os arquivos modificados
git add .

# Ou adicionar arquivos específicos
git add lib/env.ts lib/neon-connection.ts lib/database-operations.ts

# Commitar com mensagem descritiva
git commit -m "feat: implement complete Neon database integration

- Add environment variables validation with Zod
- Implement robust database connection with retry logic
- Create comprehensive database operations (CRUD)
- Add health monitoring and error handling
- Include backup/restore functionality
- Set up proper TypeScript types and interfaces

Fixes deployment errors and improves application stability"
\`\`\`

### 3. **Push para o Branch Remoto**

\`\`\`bash
# Push para o branch atual (ex: deploy)
git push origin deploy

# Ou push para main se estiver na main
git push origin main

# Forçar push se necessário (cuidado!)
git push --force-with-lease origin deploy
\`\`\`

### 4. **Criar Pull Request via GitHub CLI (Opcional)**

\`\`\`bash
# Instalar GitHub CLI se não tiver
# npm install -g @github/cli

# Login no GitHub
gh auth login

# Criar pull request
gh pr create \
  --title "feat: Complete Neon Database Integration" \
  --body "
## 🎯 Objetivo
Implementar integração completa com Neon Database para resolver erros de deployment.

## 🔧 Mudanças Implementadas
- ✅ Validação robusta de variáveis de ambiente com Zod
- ✅ Conexão resiliente com pool e retry automático
- ✅ Operações CRUD completas para mapas mentais e configurações
- ✅ Monitoramento de saúde e tratamento de erros
- ✅ Sistema de backup/restore
- ✅ Tipos TypeScript apropriados

## 🧪 Testes
- [x] Conexão com banco de dados
- [x] Operações CRUD
- [x] Health checks
- [x] Tratamento de erros
- [x] Validação de environment variables

## 📚 Documentação
- Adicionado guia de setup das variáveis de ambiente
- Documentação de deployment
- Scripts automatizados de configuração

## 🚀 Deploy
Pronto para deploy no Vercel após merge.
" \
  --base main \
  --head deploy
\`\`\`

### 5. **Criar Pull Request via Interface Web**

1. Acesse: `https://github.com/seu-usuario/mindmap-ai`
2. Clique em **"Compare & pull request"**
3. Preencha:
   - **Title**: `feat: Complete Neon Database Integration`
   - **Description**: Use o template acima
   - **Base branch**: `main`
   - **Compare branch**: `deploy`
4. Clique em **"Create pull request"**

## 📝 Template de Pull Request

\`\`\`markdown
## 🎯 Objetivo
Implementar integração completa com Neon Database para resolver erros de deployment.

## 🔧 Mudanças Implementadas
- ✅ Validação robusta de variáveis de ambiente com Zod
- ✅ Conexão resiliente com pool e retry automático  
- ✅ Operações CRUD completas para mapas mentais e configurações
- ✅ Monitoramento de saúde e tratamento de erros
- ✅ Sistema de backup/restore
- ✅ Tipos TypeScript apropriados

## 🧪 Testes Realizados
- [x] Conexão com banco de dados
- [x] Operações CRUD
- [x] Health checks  
- [x] Tratamento de erros
- [x] Validação de environment variables

## 📚 Arquivos Modificados
- `lib/env.ts` - Validação de variáveis de ambiente
- `lib/neon-connection.ts` - Conexão com Neon Database
- `lib/database-operations.ts` - Operações CRUD
- `app/api/health/route.ts` - Health checks
- `app/api/database/health/route.ts` - Health check do banco
- `.env.example` - Exemplo de variáveis
- `VERCEL_ENV_SETUP.md` - Guia de configuração

## 🚀 Próximos Passos
1. Review do código
2. Merge para main
3. Deploy no Vercel
4. Configurar variáveis de ambiente em produção

## 🔗 Links Relacionados
- [Neon Database Documentation](https://neon.tech/docs)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)
\`\`\`

## ⚡ Comandos Rápidos

\`\`\`bash
# Sequência completa para pull request
git add .
git commit -m "feat: complete Neon database integration"
git push origin deploy

# Depois criar PR via interface web ou CLI
gh pr create --title "feat: Complete Neon Database Integration" --base main --head deploy
\`\`\`

## 🔍 Verificações Finais

Antes do merge, certifique-se:

- [ ] Código revisado
- [ ] Testes passando
- [ ] Documentação atualizada
- [ ] Variáveis de ambiente documentadas
- [ ] Deploy testado

Agora você pode criar o pull request com confiança! 🎉
